/**
 * Advanced Animated Background System
 * Creates matrix rain, particles, and network visualization
 */

class AnimatedBackground {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.particles = [];
        this.matrixChars = [];
        this.networkNodes = [];
        this.dataStreams = [];
        this.animationId = null;
        
        this.init();
    }
    
    init() {
        this.createCanvas();
        this.createParticles();
        this.createMatrixRain();
        this.createNetworkNodes();
        this.createDataStreams();
        this.createScanLines();
        this.startAnimation();
        
        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());
    }
    
    createCanvas() {
        // Create main canvas for matrix effect
        this.canvas = document.createElement('canvas');
        this.canvas.className = 'matrix-canvas';
        this.ctx = this.canvas.getContext('2d');
        
        this.handleResize();
        
        // Add to matrix background
        const matrixBg = document.querySelector('.matrix-bg');
        if (matrixBg) {
            matrixBg.appendChild(this.canvas);
        } else {
            // Create matrix background if it doesn't exist
            const bg = document.createElement('div');
            bg.className = 'matrix-bg';
            bg.appendChild(this.canvas);
            document.body.insertBefore(bg, document.body.firstChild);
        }
    }
    
    handleResize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        // Reinitialize effects
        this.createMatrixRain();
    }
    
    createParticles() {
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'particles';
        document.body.appendChild(particlesContainer);
        
        // Create 50 particles
        for (let i = 0; i < 50; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            // Random position and animation delay
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 15 + 's';
            particle.style.animationDuration = (10 + Math.random() * 10) + 's';
            
            particlesContainer.appendChild(particle);
        }
    }
    
    createMatrixRain() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?';
        const fontSize = 14;
        const columns = Math.floor(this.canvas.width / fontSize);
        
        this.matrixChars = [];
        
        for (let i = 0; i < columns; i++) {
            this.matrixChars[i] = {
                x: i * fontSize,
                y: Math.random() * this.canvas.height,
                char: chars[Math.floor(Math.random() * chars.length)],
                speed: 1 + Math.random() * 3,
                opacity: Math.random()
            };
        }
    }
    
    createNetworkNodes() {
        const nodesContainer = document.createElement('div');
        nodesContainer.className = 'network-nodes';
        document.body.appendChild(nodesContainer);
        
        // Create 20 network nodes
        for (let i = 0; i < 20; i++) {
            const node = document.createElement('div');
            node.className = 'node';
            
            node.style.left = Math.random() * 100 + '%';
            node.style.top = Math.random() * 100 + '%';
            node.style.animationDelay = Math.random() * 3 + 's';
            
            nodesContainer.appendChild(node);
            this.networkNodes.push(node);
        }
    }
    
    createDataStreams() {
        const streamsContainer = document.createElement('div');
        streamsContainer.className = 'data-streams';
        document.body.appendChild(streamsContainer);
        
        // Create 15 data streams
        for (let i = 0; i < 15; i++) {
            const stream = document.createElement('div');
            stream.className = 'data-stream';
            
            stream.style.left = Math.random() * 100 + '%';
            stream.style.animationDelay = Math.random() * 5 + 's';
            stream.style.animationDuration = (3 + Math.random() * 4) + 's';
            
            streamsContainer.appendChild(stream);
        }
    }
    
    createScanLines() {
        const scanContainer = document.createElement('div');
        scanContainer.className = 'scan-lines';
        document.body.appendChild(scanContainer);
        
        // Create 3 scanning lines
        for (let i = 0; i < 3; i++) {
            const scanLine = document.createElement('div');
            scanLine.className = 'scan-line';
            
            scanLine.style.animationDelay = (i * 2.5) + 's';
            scanLine.style.animationDuration = (6 + Math.random() * 4) + 's';
            
            scanContainer.appendChild(scanLine);
        }
    }
    
    drawMatrix() {
        // Clear canvas with fade effect
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Set text properties
        this.ctx.font = '14px monospace';
        this.ctx.textAlign = 'center';
        
        // Draw matrix characters
        for (let i = 0; i < this.matrixChars.length; i++) {
            const char = this.matrixChars[i];
            
            // Set color with opacity
            this.ctx.fillStyle = `rgba(0, 255, 0, ${char.opacity})`;
            this.ctx.fillText(char.char, char.x, char.y);
            
            // Update position
            char.y += char.speed;
            
            // Reset if off screen
            if (char.y > this.canvas.height) {
                char.y = -20;
                char.char = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?'[
                    Math.floor(Math.random() * 62)
                ];
                char.opacity = Math.random();
            }
            
            // Randomly change character
            if (Math.random() < 0.01) {
                char.char = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()_+-=[]{}|;:,.<>?'[
                    Math.floor(Math.random() * 62)
                ];
            }
        }
    }
    
    startAnimation() {
        const animate = () => {
            this.drawMatrix();
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }
    
    stop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
}

// Terminal typing effect
class TerminalTyper {
    constructor(element, text, speed = 50) {
        this.element = element;
        this.text = text;
        this.speed = speed;
        this.index = 0;
    }
    
    type() {
        if (this.index < this.text.length) {
            this.element.textContent += this.text.charAt(this.index);
            this.index++;
            setTimeout(() => this.type(), this.speed);
        } else {
            // Add blinking cursor
            const cursor = document.createElement('span');
            cursor.className = 'terminal-cursor';
            this.element.appendChild(cursor);
        }
    }
    
    clear() {
        this.element.textContent = '';
        this.index = 0;
    }
}

// Glitch effect for text
class GlitchEffect {
    constructor(element) {
        this.element = element;
        this.originalText = element.textContent;
        this.glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }
    
    glitch(duration = 100) {
        const originalText = this.element.textContent;
        let glitchedText = '';
        
        for (let i = 0; i < originalText.length; i++) {
            if (Math.random() < 0.1) {
                glitchedText += this.glitchChars[Math.floor(Math.random() * this.glitchChars.length)];
            } else {
                glitchedText += originalText[i];
            }
        }
        
        this.element.textContent = glitchedText;
        
        setTimeout(() => {
            this.element.textContent = originalText;
        }, duration);
    }
    
    startRandomGlitch() {
        setInterval(() => {
            if (Math.random() < 0.05) { // 5% chance every interval
                this.glitch();
            }
        }, 1000);
    }
}

// Network visualization
class NetworkVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.nodes = [];
        this.connections = [];
        this.canvas = null;
        this.ctx = null;
        
        if (this.container) {
            this.init();
        }
    }
    
    init() {
        this.canvas = document.createElement('canvas');
        this.canvas.width = this.container.offsetWidth;
        this.canvas.height = this.container.offsetHeight;
        this.ctx = this.canvas.getContext('2d');
        
        this.container.appendChild(this.canvas);
        
        this.createNodes();
        this.animate();
    }
    
    createNodes() {
        const nodeCount = 10;
        
        for (let i = 0; i < nodeCount; i++) {
            this.nodes.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                radius: 3 + Math.random() * 3,
                pulse: Math.random() * Math.PI * 2
            });
        }
    }
    
    drawNodes() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw connections
        this.ctx.strokeStyle = 'rgba(0, 255, 0, 0.2)';
        this.ctx.lineWidth = 1;
        
        for (let i = 0; i < this.nodes.length; i++) {
            for (let j = i + 1; j < this.nodes.length; j++) {
                const dx = this.nodes[i].x - this.nodes[j].x;
                const dy = this.nodes[i].y - this.nodes[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 150) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.nodes[i].x, this.nodes[i].y);
                    this.ctx.lineTo(this.nodes[j].x, this.nodes[j].y);
                    this.ctx.stroke();
                }
            }
        }
        
        // Draw nodes
        for (let node of this.nodes) {
            const pulseSize = Math.sin(node.pulse) * 2;
            
            this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
            this.ctx.beginPath();
            this.ctx.arc(node.x, node.y, node.radius + pulseSize, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Update position
            node.x += node.vx;
            node.y += node.vy;
            node.pulse += 0.1;
            
            // Bounce off edges
            if (node.x < 0 || node.x > this.canvas.width) node.vx *= -1;
            if (node.y < 0 || node.y > this.canvas.height) node.vy *= -1;
        }
    }
    
    animate() {
        this.drawNodes();
        requestAnimationFrame(() => this.animate());
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Create animated background
    const background = new AnimatedBackground();
    
    // Add glitch effects to headers
    const headers = document.querySelectorAll('h1, h2, h3');
    headers.forEach(header => {
        const glitch = new GlitchEffect(header);
        glitch.startRandomGlitch();
    });
    
    // Add typing effect to terminal-like elements
    const terminalElements = document.querySelectorAll('.terminal-text');
    terminalElements.forEach(element => {
        const originalText = element.textContent;
        element.textContent = '';
        const typer = new TerminalTyper(element, originalText);
        typer.type();
    });
    
    // Initialize network visualizer if container exists
    const networkViz = new NetworkVisualizer('network-visualization');
    
    // Add holographic effect to cards
    const cards = document.querySelectorAll('.card, .payload-card, .host-card');
    cards.forEach(card => {
        card.classList.add('holographic');
    });
    
    // Performance optimization: reduce animations on mobile
    if (window.innerWidth < 768) {
        document.body.classList.add('mobile-optimized');
    }
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        background.stop();
    });
});

// Export for use in other scripts
window.AnimatedBackground = AnimatedBackground;
window.TerminalTyper = TerminalTyper;
window.GlitchEffect = GlitchEffect;
window.NetworkVisualizer = NetworkVisualizer;
