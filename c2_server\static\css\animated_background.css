/* Advanced Animated Background for C2 Dashboard */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

/* Matrix Rain Effect */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.matrix-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

/* Animated Grid */
.grid-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-image: 
        linear-gradient(rgba(0, 255, 0, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 0, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Floating Particles */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #00ff00;
    border-radius: 50%;
    animation: float 15s infinite linear;
    opacity: 0.7;
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.7;
    }
    90% {
        opacity: 0.7;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* Scanning Lines */
.scan-lines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #00ff00, transparent);
    animation: scan 8s infinite linear;
    opacity: 0.5;
}

@keyframes scan {
    0% {
        top: -2px;
        opacity: 0;
    }
    10% {
        opacity: 0.5;
    }
    90% {
        opacity: 0.5;
    }
    100% {
        top: 100vh;
        opacity: 0;
    }
}

/* Glitch Effect */
.glitch-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    background: repeating-linear-gradient(
        0deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 0, 0.03) 2px,
        rgba(0, 255, 0, 0.03) 4px
    );
    animation: glitchMove 0.1s infinite;
}

@keyframes glitchMove {
    0% { transform: translateX(0); }
    20% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    60% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    100% { transform: translateX(0); }
}

/* Pulsing Nodes */
.network-nodes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.node {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #00ff00;
    border-radius: 50%;
    animation: pulse 3s infinite ease-in-out;
}

.node::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 24px;
    height: 24px;
    border: 1px solid #00ff00;
    border-radius: 50%;
    animation: ripple 3s infinite ease-out;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.5);
    }
}

@keyframes ripple {
    0% {
        opacity: 1;
        transform: scale(0);
    }
    100% {
        opacity: 0;
        transform: scale(1);
    }
}

/* Data Streams */
.data-streams {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.data-stream {
    position: absolute;
    width: 1px;
    height: 100px;
    background: linear-gradient(to bottom, transparent, #00ff00, transparent);
    animation: stream 5s infinite linear;
    opacity: 0.6;
}

@keyframes stream {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Terminal Cursor */
.terminal-cursor {
    display: inline-block;
    width: 2px;
    height: 1.2em;
    background: #00ff00;
    animation: blink 1s infinite;
    margin-left: 2px;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Enhanced Typography */
body {
    font-family: 'Orbitron', monospace;
    background: #000;
    color: #00ff00;
    overflow-x: hidden;
}

/* Neon Glow Effects */
.neon-text {
    text-shadow: 
        0 0 5px #00ff00,
        0 0 10px #00ff00,
        0 0 15px #00ff00,
        0 0 20px #00ff00;
}

.neon-border {
    border: 1px solid #00ff00;
    box-shadow: 
        0 0 5px #00ff00,
        inset 0 0 5px #00ff00;
}

/* Holographic Effect */
.holographic {
    background: linear-gradient(45deg, 
        rgba(0, 255, 0, 0.1) 0%,
        rgba(0, 255, 255, 0.1) 25%,
        rgba(255, 0, 255, 0.1) 50%,
        rgba(255, 255, 0, 0.1) 75%,
        rgba(0, 255, 0, 0.1) 100%);
    background-size: 400% 400%;
    animation: hologram 4s ease infinite;
}

@keyframes hologram {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Circuit Board Pattern */
.circuit-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background-image: 
        radial-gradient(circle at 20% 20%, rgba(0, 255, 0, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(0, 255, 0, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(0, 255, 0, 0.1) 1px, transparent 1px);
    background-size: 100px 100px, 150px 150px, 200px 200px;
    animation: circuitMove 30s linear infinite;
}

@keyframes circuitMove {
    0% { transform: translate(0, 0) rotate(0deg); }
    100% { transform: translate(50px, 50px) rotate(360deg); }
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 255, 0, 0.3);
    border-top: 3px solid #00ff00;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-online {
    color: #00ff00;
    text-shadow: 0 0 10px #00ff00;
    animation: pulse 2s infinite;
}

.status-offline {
    color: #ff4444;
    text-shadow: 0 0 10px #ff4444;
}

.status-warning {
    color: #ffaa00;
    text-shadow: 0 0 10px #ffaa00;
    animation: pulse 1s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .matrix-bg, .grid-overlay, .particles, .scan-lines {
        opacity: 0.5;
    }
    
    .particle {
        animation-duration: 20s;
    }
    
    .scan-line {
        animation-duration: 12s;
    }
}

/* High Performance Mode */
@media (prefers-reduced-motion: reduce) {
    .matrix-bg, .grid-overlay, .particles, .scan-lines,
    .glitch-overlay, .network-nodes, .data-streams {
        animation: none;
    }
    
    .particle, .scan-line, .node, .data-stream {
        display: none;
    }
}
