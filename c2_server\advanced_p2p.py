#!/usr/bin/env python3
"""
Advanced P2P Network Implementation
Fully functional peer-to-peer communication with mesh networking
"""

import os
import json
import time
import socket
import threading
import hashlib
import random
import struct
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class P2PNode:
    """Advanced P2P node with full mesh networking capabilities"""
    
    def __init__(self, port=9999, max_peers=50):
        self.port = port
        self.max_peers = max_peers
        self.node_id = hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        self.peers = {}
        self.server_socket = None
        self.running = False
        self.message_cache = {}
        self.routing_table = {}
        self.heartbeat_interval = 30
        
    def start(self):
        """Start P2P node"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.port))
            self.server_socket.listen(self.max_peers)
            self.running = True
            
            # Start server thread
            server_thread = threading.Thread(target=self._server_loop, daemon=True)
            server_thread.start()
            
            # Start heartbeat thread
            heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            heartbeat_thread.start()
            
            # Start peer discovery
            discovery_thread = threading.Thread(target=self._peer_discovery, daemon=True)
            discovery_thread.start()
            
            logger.info(f"🌐 P2P node {self.node_id} started on port {self.port}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start P2P node: {e}")
            return False
    
    def _server_loop(self):
        """Main server loop for accepting connections"""
        while self.running:
            try:
                client_socket, address = self.server_socket.accept()
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, address),
                    daemon=True
                )
                client_thread.start()
            except Exception as e:
                if self.running:
                    logger.error(f"Server loop error: {e}")
    
    def _handle_client(self, client_socket, address):
        """Handle incoming client connection"""
        try:
            # Receive handshake
            data = client_socket.recv(4096)
            if data:
                message = json.loads(data.decode())
                if message.get('type') == 'handshake':
                    peer_id = message.get('node_id')
                    if peer_id and len(self.peers) < self.max_peers:
                        # Add peer
                        self.peers[peer_id] = {
                            'socket': client_socket,
                            'address': address,
                            'last_seen': time.time(),
                            'connected': True
                        }
                        
                        # Send handshake response
                        response = {
                            'type': 'handshake_response',
                            'node_id': self.node_id,
                            'peers': list(self.peers.keys())
                        }
                        client_socket.send(json.dumps(response).encode())
                        
                        logger.info(f"🤝 Peer connected: {peer_id} from {address}")
                        
                        # Handle messages from this peer
                        self._handle_peer_messages(client_socket, peer_id)
                    else:
                        client_socket.close()
                else:
                    client_socket.close()
            else:
                client_socket.close()
                
        except Exception as e:
            logger.error(f"Client handling error: {e}")
            if client_socket:
                client_socket.close()
    
    def _handle_peer_messages(self, peer_socket, peer_id):
        """Handle messages from a connected peer"""
        while self.running and peer_id in self.peers:
            try:
                data = peer_socket.recv(4096)
                if data:
                    message = json.loads(data.decode())
                    self._process_message(message, peer_id)
                    self.peers[peer_id]['last_seen'] = time.time()
                else:
                    break
            except Exception as e:
                logger.error(f"Peer message error: {e}")
                break
        
        # Clean up disconnected peer
        if peer_id in self.peers:
            del self.peers[peer_id]
            logger.info(f"🔌 Peer disconnected: {peer_id}")
    
    def _process_message(self, message, sender_id):
        """Process incoming P2P message"""
        try:
            msg_type = message.get('type')
            msg_id = message.get('id', '')
            
            # Prevent message loops
            if msg_id in self.message_cache:
                return
            
            self.message_cache[msg_id] = time.time()
            
            if msg_type == 'peer_discovery':
                self._handle_peer_discovery(message, sender_id)
            elif msg_type == 'data_relay':
                self._handle_data_relay(message, sender_id)
            elif msg_type == 'heartbeat':
                self._handle_heartbeat(message, sender_id)
            elif msg_type == 'route_update':
                self._handle_route_update(message, sender_id)
            
            # Forward message to other peers (flooding)
            self._forward_message(message, sender_id)
            
        except Exception as e:
            logger.error(f"Message processing error: {e}")
    
    def _handle_peer_discovery(self, message, sender_id):
        """Handle peer discovery message"""
        discovered_peers = message.get('peers', [])
        for peer_info in discovered_peers:
            peer_id = peer_info.get('id')
            peer_address = peer_info.get('address')
            
            if peer_id != self.node_id and peer_id not in self.peers:
                # Try to connect to discovered peer
                self._connect_to_peer(peer_address[0], peer_address[1])
    
    def _handle_data_relay(self, message, sender_id):
        """Handle data relay message"""
        target_id = message.get('target')
        if target_id == self.node_id:
            # Message is for us
            data = message.get('data')
            logger.info(f"📨 Received P2P message: {data}")
        else:
            # Forward to target if we know the route
            if target_id in self.routing_table:
                next_hop = self.routing_table[target_id]
                if next_hop in self.peers:
                    self._send_to_peer(next_hop, message)
    
    def _handle_heartbeat(self, message, sender_id):
        """Handle heartbeat message"""
        if sender_id in self.peers:
            self.peers[sender_id]['last_seen'] = time.time()
    
    def _handle_route_update(self, message, sender_id):
        """Handle routing table update"""
        routes = message.get('routes', {})
        for target, next_hop in routes.items():
            if target != self.node_id:
                self.routing_table[target] = sender_id
    
    def _forward_message(self, message, sender_id):
        """Forward message to other peers"""
        for peer_id in self.peers:
            if peer_id != sender_id:
                self._send_to_peer(peer_id, message)
    
    def _send_to_peer(self, peer_id, message):
        """Send message to specific peer"""
        try:
            if peer_id in self.peers:
                peer_socket = self.peers[peer_id]['socket']
                peer_socket.send(json.dumps(message).encode())
        except Exception as e:
            logger.error(f"Failed to send to peer {peer_id}: {e}")
            # Remove failed peer
            if peer_id in self.peers:
                del self.peers[peer_id]
    
    def _heartbeat_loop(self):
        """Send periodic heartbeats"""
        while self.running:
            try:
                heartbeat_msg = {
                    'type': 'heartbeat',
                    'id': hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest(),
                    'node_id': self.node_id,
                    'timestamp': time.time()
                }
                
                self.broadcast_message(heartbeat_msg)
                
                # Clean up old message cache
                current_time = time.time()
                old_messages = [msg_id for msg_id, timestamp in self.message_cache.items() 
                              if current_time - timestamp > 300]  # 5 minutes
                for msg_id in old_messages:
                    del self.message_cache[msg_id]
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                time.sleep(self.heartbeat_interval)
    
    def _peer_discovery(self):
        """Discover new peers"""
        while self.running:
            try:
                # Broadcast peer discovery
                discovery_msg = {
                    'type': 'peer_discovery',
                    'id': hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest(),
                    'node_id': self.node_id,
                    'peers': [
                        {
                            'id': peer_id,
                            'address': peer_info['address']
                        }
                        for peer_id, peer_info in self.peers.items()
                    ]
                }
                
                self.broadcast_message(discovery_msg)
                
                # Try to connect to bootstrap nodes
                bootstrap_nodes = [
                    ('127.0.0.1', 9998),
                    ('127.0.0.1', 10000),
                    ('127.0.0.1', 10001)
                ]
                
                for host, port in bootstrap_nodes:
                    if len(self.peers) < self.max_peers:
                        self._connect_to_peer(host, port)
                
                time.sleep(60)  # Discovery every minute
                
            except Exception as e:
                logger.error(f"Peer discovery error: {e}")
                time.sleep(60)
    
    def _connect_to_peer(self, host, port):
        """Connect to a peer"""
        try:
            if len(self.peers) >= self.max_peers:
                return False
            
            peer_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            peer_socket.settimeout(10)
            peer_socket.connect((host, port))
            
            # Send handshake
            handshake = {
                'type': 'handshake',
                'node_id': self.node_id
            }
            peer_socket.send(json.dumps(handshake).encode())
            
            # Receive response
            response = peer_socket.recv(4096)
            if response:
                data = json.loads(response.decode())
                if data.get('type') == 'handshake_response':
                    peer_id = data.get('node_id')
                    if peer_id and peer_id != self.node_id:
                        self.peers[peer_id] = {
                            'socket': peer_socket,
                            'address': (host, port),
                            'last_seen': time.time(),
                            'connected': True
                        }
                        
                        logger.info(f"🔗 Connected to peer: {peer_id}")
                        
                        # Start handling messages
                        peer_thread = threading.Thread(
                            target=self._handle_peer_messages,
                            args=(peer_socket, peer_id),
                            daemon=True
                        )
                        peer_thread.start()
                        
                        return True
            
            peer_socket.close()
            return False
            
        except Exception as e:
            logger.debug(f"Failed to connect to {host}:{port}: {e}")
            return False
    
    def broadcast_message(self, message):
        """Broadcast message to all peers"""
        for peer_id in list(self.peers.keys()):
            self._send_to_peer(peer_id, message)
    
    def send_data(self, target_id, data):
        """Send data to specific target through P2P network"""
        message = {
            'type': 'data_relay',
            'id': hashlib.sha256(f"{time.time()}{random.random()}".encode()).hexdigest(),
            'source': self.node_id,
            'target': target_id,
            'data': data,
            'timestamp': time.time()
        }
        
        if target_id in self.peers:
            # Direct connection
            self._send_to_peer(target_id, message)
        else:
            # Broadcast for routing
            self.broadcast_message(message)
    
    def get_network_stats(self):
        """Get P2P network statistics"""
        return {
            'node_id': self.node_id,
            'connected_peers': len(self.peers),
            'routing_table_size': len(self.routing_table),
            'message_cache_size': len(self.message_cache),
            'peers': list(self.peers.keys())
        }
    
    def stop(self):
        """Stop P2P node"""
        self.running = False
        
        # Close all peer connections
        for peer_id, peer_info in self.peers.items():
            try:
                peer_info['socket'].close()
            except:
                pass
        
        # Close server socket
        if self.server_socket:
            self.server_socket.close()
        
        logger.info(f"🛑 P2P node {self.node_id} stopped")
